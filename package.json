{"name": "vue_template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:debug": "vite --mode debug", "build": "vue-tsc -b && vite build --mode production", "build:debug": "vue-tsc -b && vite build --mode debug", "build:dev": "vue-tsc -b && vite build --mode development", "preview": "vite preview", "preview:debug": "vite preview --mode debug"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.9.0", "echarts": "^6.0.0", "element-plus": "^2.10.1", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}