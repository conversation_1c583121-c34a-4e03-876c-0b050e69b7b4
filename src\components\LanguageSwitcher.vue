<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { saveLanguage } from '@/locales/index.js'
import {ArrowUp, Check} from "@element-plus/icons-vue";

const { locale, t } = useI18n()
const showLanguageMenu = ref(false)

// 语言选项
const languages = [
  { code: 'zh-CN', name: '中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
  { code: 'th-TH', name: 'ไทย', flag: '🇹🇭' }
]

// 当前语言
const currentLanguage = computed(() => {
  return languages.find(lang => lang.code === locale.value) || languages[0]
})

// 切换语言
const switchLanguage = (langCode) => {
  locale.value = langCode
  saveLanguage(langCode)
  showLanguageMenu.value = false
}

// 切换菜单显示状态
const toggleMenu = () => {
  showLanguageMenu.value = !showLanguageMenu.value
}

// 点击外部关闭菜单
const closeMenu = () => {
  showLanguageMenu.value = false
}
</script>

<template>
  <div class="language-switcher">
    <!-- 语言菜单 -->
    <transition name="menu-fade">
      <div 
        v-if="showLanguageMenu" 
        class="language-menu"
        @click.stop
      >
        <div class="menu-header">
          <span>{{ currentLanguage.name}}</span>
        </div>
        <div 
          v-for="language in languages" 
          :key="language.code"
          class="language-option"
          :class="{ active: language.code === locale }"
          @click="switchLanguage(language.code)"
        >
          <span class="flag">{{ language.flag }}</span>
          <span class="name">{{ language.name }}</span>
          <el-icon v-if="language.code === locale" class="check-icon">
            <Check />
          </el-icon>
        </div>
      </div>
    </transition>
    
    <!-- 悬浮按钮 -->
    <div 
      class="language-button"
      @click="toggleMenu"
      :class="{ active: showLanguageMenu }"
    >
      <span class="current-flag">{{ currentLanguage.flag }}</span>
      <el-icon class="arrow-icon" :class="{ rotate: showLanguageMenu }">
        <ArrowUp />
      </el-icon>
    </div>
    
    <!-- 遮罩层 -->
    <div 
      v-if="showLanguageMenu"
      class="overlay"
      @click="closeMenu"
    ></div>
  </div>
</template>

<style scoped>
.language-switcher {
  position: fixed;
  bottom: 50px;
  right: 20px;
  z-index: 9999;
}

.language-button {
  width: 56px;
  height: 56px;
  background: #33A1E0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.language-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.language-button:hover::before {
  left: 100%;
}

.language-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.language-button.active {
  transform: translateY(-2px) scale(1.05);
  background: #33A1E0;
}

.current-flag {
  font-size: 20px;
  margin-right: 4px;
}

.arrow-icon {
  font-size: 14px;
  color: white;
  transition: transform 0.3s ease;
}

.arrow-icon.rotate {
  transform: rotate(180deg);
}

.language-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-width: 160px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.menu-header {
  background: #33A1E0;
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.language-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.language-option:hover {
  background: #f8f9fa;
  transform: translateX(2px);
}

.language-option.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  font-weight: 600;
}

.language-option:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.flag {
  font-size: 18px;
  margin-right: 12px;
}

.name {
  flex: 1;
  font-size: 14px;
}

.check-icon {
  color: #67c23a;
  font-size: 16px;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}


.menu-fade-enter-active,
.menu-fade-leave-active {
  transition: all 0.3s ease;
}

.menu-fade-enter-from {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
}

.menu-fade-leave-to {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-switcher {
    bottom: 35px;
    right: 15px;
  }
  
  .language-button {
    width: 50px;
    height: 50px;
  }
  
  .current-flag {
    font-size: 18px;
  }
  
  .language-menu {
    bottom: 60px;
    min-width: 140px;
  }
  
  .language-option {
    padding: 10px 14px;
  }
  
  .flag {
    font-size: 16px;
    margin-right: 10px;
  }
  
  .name {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .language-switcher {
    bottom: 30px;
    right: 10px;
  }
  
  .language-button {
    width: 45px;
    height: 45px;
  }
  
  .current-flag {
    font-size: 16px;
  }
  
  .language-menu {
    bottom: 55px;
    min-width: 130px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .language-switcher {
    bottom: max(20px, env(safe-area-inset-bottom));
    right: max(20px, env(safe-area-inset-right));
  }
  
  @media (max-width: 768px) {
    .language-switcher {
      bottom: max(15px, env(safe-area-inset-bottom));
      right: max(15px, env(safe-area-inset-right));
    }
  }
  
  @media (max-width: 480px) {
    .language-switcher {
      bottom: max(40px, env(safe-area-inset-bottom));
      right: max(10px, env(safe-area-inset-right));
    }
  }
}
</style>
