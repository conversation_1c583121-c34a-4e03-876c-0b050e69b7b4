import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig, AxiosError } from 'axios'
import { useAppStore } from '@/store'
import {useRouter} from "vue-router";


const router = useRouter();


// 创建Axios实例
const service: AxiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || import.meta.env.BASE_URL || '/',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    }
});


// === 工具函数 ===

async function checkResponse(res: any) {
  if (res.headers?.vol_exp === '1') {
    await replaceToken()
  }
}

const _Authorization = 'Authorization'



function getToken() {
  const appStore = useAppStore()
  axios.defaults.headers['serviceId'] = localStorage.getItem('serviceId') || ''
  axios.defaults.headers['deptId'] = localStorage.getItem('deptId') || ''
  return appStore.getToken()
}

// 请求拦截器
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 设置认证token
        config.headers[_Authorization] = getToken()
        return config
    },
    (error: AxiosError) => {
        return Promise.reject(error)
    }
)



async function replaceToken() {
  const appStore = useAppStore()
  // 使用 axios 直接调用
  const response =await axios.post('/api/User/replaceToken')
  const x = response.data
  if (x.status) {
    const info = appStore.getUserInfo()()
    if (x.data?.accessToken) {
        info.token = x.data.token
        info.accessToken = x.data.accessToken
    } else {
        info.token = x.data
    }
    appStore.setUserInfo(info)
} else {
    await router.push({ name: 'login' })
}
}



// 响应拦截器
service.interceptors.response.use(
    async (res: AxiosResponse) => {

        await checkResponse(res)
        return Promise.resolve(res.data)
    },
    (error: AxiosError) => {
        return Promise.reject(error)
    }
)


// 通用请求方法，增加类型安全
export const request = {
    get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
        return service.get(url, { params, ...config })
    },
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig):  Promise<T>{
        return service.post(url, data, config)
    },
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig):  Promise<T>{
        return service.put(url, data, config)
    },
    delete<T = any>(url: string, config?: AxiosRequestConfig):  Promise<T> {
        return service.delete(url, config)
    }
}
