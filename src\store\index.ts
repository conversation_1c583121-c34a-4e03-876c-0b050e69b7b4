// stores/appStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'


function loadUserInfo(userInfoRef: any) {
    if (userInfoRef.value) return userInfoRef.value
    const userInfoStr = localStorage.getItem('user')
    if (userInfoStr) {
        try {
            userInfoRef.value = JSON.parse(userInfoStr)
        } catch {
            // ignore
        }
    }
    return userInfoRef.value || {}
}

export const useAppStore = defineStore('app', () => {
    // state
    const data = ref<Record<string, any>>({})
    const permission = ref<any[]>([])
    const isLoading = ref(false)
    const userInfo = ref<any | null>(null)
    const appLang = ref<Record<string, any>>({})
    const serviceList = ref<any[]>([])


    // getters (computed)
    const getServiceList = computed(() => () => serviceList.value || [])

    const local = computed(() => () => appLang.value || {})


    const getPermission = computed(() => (path?: string) => {
        if (!path) return permission.value
        return permission.value.find((x) => x.path === path)
    })

    const getUserInfo = computed(() => () => {
        loadUserInfo(userInfo)
        return userInfo.value
    })

    const getUserName = computed(() => () => {
        loadUserInfo(userInfo)
        return userInfo.value?.userName || '未获取到登陆信息'
    })

    const getToken = computed(() => () => {
        loadUserInfo(userInfo)
        return userInfo.value ? 'Bearer ' + userInfo.value.token : ''
    })

    const isLogin = computed(() => () => {
        return !!loadUserInfo(userInfo)
    })

    const isLoadingGetter = computed(() => () => isLoading.value)
    const dataGetter = computed(() => () => data.value)
    const getData = computed(() => () => data.value)

    // actions
    function setLocal(source: Record<string, any>) {
        appLang.value = source
    }

    function setPermissionAction(dataInput: any) {
        if (!dataInput || typeof dataInput !== 'object') return
        if (Array.isArray(dataInput)) {
            permission.value.push(...dataInput)
        } else {
            permission.value = dataInput
        }
    }

    function setUserInfo(dataInput: any) {
        userInfo.value = dataInput
        localStorage.setItem('user', JSON.stringify(dataInput))
    }

    function clearUserInfo() {
        permission.value = []
        userInfo.value = null
        localStorage.removeItem('user')
    }

    function test() {
        return 113344
    }

    function updateLoadingState(flag: boolean) {
        isLoading.value = flag
    }

    function setServiceList(dataInput: any[]) {
        serviceList.value = dataInput
    }

    // 原 Vuex actions 转过来
    function setPermissionDispatch(dataInput: any) {
        setPermissionAction(dataInput)
    }

    function toDo() {
        // Vuex 中 context.Store.m 没有实际含义，这里暂时空实现
        return null
    }

    function onLoading(flag: boolean) {
        updateLoadingState(flag)
    }

    return {
        // state
        data,
        permission,
        isLoading,
        userInfo,
        appLang,
        serviceList,

        // getters
        getServiceList,
        local,
        getPermission,
        getUserInfo,
        getUserName,
        getToken,
        isLogin,
        isLoadingGetter,
        dataGetter,
        getData,

        // actions
        setLocal,
        setPermissionAction,
        setUserInfo,
        clearUserInfo,
        test,
        updateLoadingState,
        setServiceList,
        setPermissionDispatch,
        toDo,
        onLoading
    }
})
