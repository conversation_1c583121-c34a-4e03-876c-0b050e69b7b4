<script setup lang="ts">
import {RouterView} from 'vue-router'
</script>

<template>
  <div class="app">
    <RouterView />
  </div>

</template>

<style scoped>
.app{
  width: 100%;
}
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
