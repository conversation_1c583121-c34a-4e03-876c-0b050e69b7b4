import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN.js'
import enUS from './en-US.js'
import thTH from './th-TH.js'

// 从localStorage获取保存的语言设置，默认为中文
const getStoredLanguage = () => {
  const stored = localStorage.getItem('pda-language')
  return stored || 'zh-CN'
}

// 保存语言设置到localStorage
export const saveLanguage = (locale) => {
  localStorage.setItem('pda-language', locale)
}


// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用Composition API模式
  locale: getStoredLanguage(), // 设置默认语言
  fallbackLocale: 'zh-CN', // 设置备用语言
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'th-TH': thTH
  }
})

export default i18n
