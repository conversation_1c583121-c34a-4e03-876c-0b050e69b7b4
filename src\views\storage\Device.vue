<script setup >
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Menu, DataBoard, Cpu, MoreFilled } from '@element-plus/icons-vue';
import { useDeviceStore } from '../../store/device';
import { ElLoading } from 'element-plus';

const router = useRouter();
const route = useRoute();
const deviceStore = useDeviceStore();

const activeMenu = ref('overview');

const handleMenuSelect = (index) => {
  let routeName = '';
  switch (index) {
    case 'overview':
      routeName = 'device_overview';
      break;
    case 'stacker':
      routeName = 'device_stacker';
      // 查找第一个堆垛机设备
      const stackerDevice = deviceStore.getPlcDevices.find(device => device.Name.includes('堆垛机'));
      if (stackerDevice) {
        deviceStore.setSelectedPlc(stackerDevice.Id);
        return router.push({ 
          name: routeName,
          query: { deviceId: stackerDevice.Id }
        });
      }
      break;
    case 'conveyor':
      routeName = 'device_conveyor';
      // 查找第一个输送线设备
      const conveyorDevice = deviceStore.getPlcDevices.find(device => device.Name.includes('输送线'));
      if (conveyorDevice) {
        deviceStore.setSelectedPlc(conveyorDevice.Id);
        return router.push({ 
          name: routeName,
          query: { deviceId: conveyorDevice.Id }
        });
      }
      break;
    case 'shuttle':
      routeName = '';
      // 查找第一个输送线设备
      // const conveyorDevice = deviceStore.getPlcDevices.find(device => device.Name.includes('输送线'));
      // if (conveyorDevice) {
      //   deviceStore.setSelectedPlc(conveyorDevice.Id);
      //   return router.push({
      //     name: routeName,
      //     query: { deviceId: conveyorDevice.Id }
      //   });
      // }
      break;
    case 'agv':
      routeName = '';
      // 查找第一个输送线设备
      // const conveyorDevice = deviceStore.getPlcDevices.find(device => device.Name.includes('输送线'));
      // if (conveyorDevice) {
      //   deviceStore.setSelectedPlc(conveyorDevice.Id);
      //   return router.push({
      //     name: routeName,
      //     query: { deviceId: conveyorDevice.Id }
      //   });
      // }
      break;
    case 'camera':
      routeName = '';
      // 查找第一个输送线设备
      // const conveyorDevice = deviceStore.getPlcDevices.find(device => device.Name.includes('输送线'));
      // if (conveyorDevice) {
      //   deviceStore.setSelectedPlc(conveyorDevice.Id);
      //   return router.push({
      //     name: routeName,
      //     query: { deviceId: conveyorDevice.Id }
      //   });
      // }
      break;

    default:

      return; 
  }
  router.push({ name: routeName });
};

// 监听路由变化，更新菜单高亮状态
watch(route, (newRoute) => {
  const routeNameToMenuIndex = {
    'device_overview': 'overview',
    'device_stacker': 'stacker',
    'device_conveyor': 'conveyor',
  };
  activeMenu.value = routeNameToMenuIndex[newRoute.name] || 'overview';
}, { immediate: true });

onMounted(async () => {
  // 加载PLC设备数据
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '加载设备数据中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  
  try {
    await deviceStore.fetchPlcDevices();
    
    // 确保初次加载时在总览页
    if (route.name === 'storage_device') {
      console.log('准备重定向到设备总览页');
      router.push({ 
        name: 'device_overview' 
      }).catch(err => {
        console.error('重定向到设备总览页失败:', err);
        // 尝试使用完整路径
        router.push('/Wcs_Simulator/storage_device/overview').catch(e => {
          console.error('使用完整路径重定向也失败:', e);
        });
      });
    }
  } catch (error) {
    console.error('加载设备数据失败:', error);
  } finally {
    loadingInstance.close();
  }
})
</script>

<template>
  <div class="device-container">
    <el-card class="device-nav" shadow="never">
       <div class="nav-header">
         <el-icon><Menu /></el-icon>
         <span>设备列表</span>
       </div>
       <el-menu :default-active="activeMenu" class="device-menu" @select="handleMenuSelect">
         <el-menu-item index="overview">
           <el-icon><DataBoard /></el-icon>
           <span>设备总览</span>
         </el-menu-item>
         <el-sub-menu index="main-devices">
           <template #title>
             <el-icon><Cpu /></el-icon>
             <span>核心设备</span>
           </template>
           <el-menu-item index="stacker">堆垛机</el-menu-item>
           <el-menu-item index="conveyor">输送线</el-menu-item>
         </el-sub-menu>
         <el-sub-menu index="other-devices">
           <template #title>
             <el-icon><MoreFilled /></el-icon>
             <span>其他设备</span>
           </template>
           <el-menu-item index="shuttle" disabled>穿梭车</el-menu-item>
           <el-menu-item index="agv" disabled>AGV</el-menu-item>
           <el-menu-item index="camera" disabled>摄像头</el-menu-item>
         </el-sub-menu>
       </el-menu>
    </el-card>
    <div class="device-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<style scoped lang="less">
.device-container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  gap: 16px;
  padding: 16px;
}
.device-nav {
  width: 240px;
  flex-shrink: 0;
}
.nav-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 10px;
  padding: 10px;
}
.nav-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}
.device-menu {
  border-right: none;
}
.device-menu .el-menu-item.is-active {
  background-color: #ecf5ff;
  border-right: 3px solid var(--el-color-primary);
}
.device-content {
  flex-grow: 1;
  overflow: auto;
}
:deep(.device-nav .el-card__body) {
  padding: 10px 0;
}
</style>