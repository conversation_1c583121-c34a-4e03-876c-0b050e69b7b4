<template>
  <div class="wcs-simulator-container">
    <router-view v-if="hasToken" class="simulator-view"  />
    <div
        class="drawer-handle"
        :class="{
        'drawer-handle-right': isDrawerRight,
        'drawer-expanded': drawerVisible,
        'drawer-left-expand': drawerVisible && !isDrawerRight,
        'drawer-right-expand': drawerVisible && isDrawerRight
      }"
        @mousedown="startDrag"
        ref="drawerHandleRef"
        :style="drawerPosition"
    >
      <div class="drawer-icons">
        <div v-for="(item, index) in navItems" :key="index" class="drawer-icon" @click="navigateTo(item.route)">
          <span class="icon-wrapper">
            <img :src="item.icon" class="nav-icon" alt="" />
          </span>
          <span v-show="drawerVisible" class="drawer-icon-text">{{ item.name }}</span>
        </div>
      </div>

      <div class="drawer-toggle" @click.stop="toggleDrawer">
        <el-icon v-if="!drawerVisible && !isDrawerRight"><ArrowRight /></el-icon>
        <el-icon v-if="!drawerVisible && isDrawerRight"><ArrowLeft /></el-icon>
        <el-icon v-if="drawerVisible && !isDrawerRight"><ArrowLeft /></el-icon>
        <el-icon v-if="drawerVisible && isDrawerRight"><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from "@/store";
import {request} from "@/utils/request.ts";


import {
  ArrowLeft,
  ArrowRight,

} from '@element-plus/icons-vue'


import a1 from '@/assets/icon/home.png'
import a2 from '@/assets/icon/任务.png'
import a3 from '@/assets/icon/机器人.png'
import a4 from '@/assets/icon/数据看板.png'
import a5 from '@/assets/icon/history.png'

const store = useAppStore()

// 路由实例
const router = useRouter()

// const warehouseStore = useWarehouseStore()
// const stationStore = useStationStore()

// 状态变量
const hasToken = computed(() => store.isLogin())
const loading = ref(false)

const codeImgSrc = ref('')
const drawerVisible = ref(false)
const isDrawerRight = ref(false) // 默认在左侧
const activeRoute = ref('storage_home')
const drawerHandleRef = ref(null)
const isDragging = ref(false)
const drawerPosition = reactive({
  left: '20px',
  top: '50%',
  transform: 'translateY(-50%)'
})

// 导航项
const navItems = [
  { name: '首页', icon: a1, route: 'storage_home' },
  { name: '设备', icon: a3, route: 'storage_device' },
  { name: 'WCS任务', icon: a2, route: 'storage_WmsTask' },
  { name: '历史信息', icon:a5, route: 'storage_history' },
  { name: '看板', icon: a4, route: 'storage_Information' }
]

// 全局属性
const appContext = getCurrentInstance().appContext
const $message = appContext.config.globalProperties.$message || {
  error: (msg) => { alert(msg) },
  success: (msg) => { alert(msg) }
}
const $ts = appContext.config.globalProperties.$ts || ((text) => Array.isArray(text) ? text.join('') : text)




// 抽屉相关方法
const toggleDrawer = () => {
  // 切换抽屉显示状态
  drawerVisible.value = !drawerVisible.value

  // 根据位置确定展开方向
  if (drawerHandleRef.value) {
    const container = document.querySelector('.wcs-simulator-container')
    const containerRect = container.getBoundingClientRect()
    const drawerRect = drawerHandleRef.value.getBoundingClientRect()

    // 计算相对于容器中心的位置
    const drawerCenterX = drawerRect.left + drawerRect.width / 2 - containerRect.left
    isDrawerRight.value = drawerCenterX > containerRect.width / 2
  }
}

const closeDrawer = () => {
  drawerVisible.value = false
}

const navigateTo = (route) => {
  activeRoute.value = route
  closeDrawer()
  console.log('正在跳转到路由:', route)

  // 特殊处理storage_Information，因为它是顶级路由（全屏显示）
  if (route === 'storage_Information') {
    router.push({
      name: route,
    }).catch(err => {
      console.error('路由跳转失败:', err)
      // 尝试直接使用路径跳转
      router.push('/storage_Information')
    })
  } else {
    // 其他路由作为子路由处理
    router.push({
      name: route,
    }).catch(err => {
      console.error('路由跳转失败:', err)
      // 尝试直接使用路径跳转
      const path = `/Wcs_Simulator/${route}`
      console.log('尝试使用路径跳转:', path)
      router.push(path)
    })
  }
}

// 拖拽相关方法
const startDrag = (event) => {
  // 只响应鼠标左键（左键为 button = 0）
  if (event.button !== 0) return

  // 阻止事件传播，避免触发其他点击事件
  event.stopPropagation()

  isDragging.value = true

  // 获取Wcs_Simulator容器
  const container = document.querySelector('.wcs-simulator-container')
  // 获取容器的位置尺寸返回DOMRect 对象
  const containerRect = container.getBoundingClientRect()

  // 6. 记录鼠标起始位置
  const initialX = event.clientX
  const initialY = event.clientY
  // 7. 获取拖拽句柄当前的位置
  const handleRect = drawerHandleRef.value.getBoundingClientRect()
  // 8. 计算鼠标点击位置相对于句柄左上角的偏移（用于固定光标在句柄中的相对位置）
  const offsetX = initialX - handleRect.left
  const offsetY = initialY - handleRect.top

  // 移动处理函数
  const handleMouseMove = (moveEvent) => {
    if (!isDragging.value) return
    // 当前鼠标相对于页面的位置，减去点击偏移量，得到句柄新坐标
    const x = moveEvent.clientX - offsetX
    const y = moveEvent.clientY - offsetY

    // 计算相对于容器的位置
    const relativeX = x - containerRect.left
    const relativeY = y - containerRect.top

    // 确保不超出容器边界，获取句柄 DOM 元素及其尺寸
    const handle = drawerHandleRef.value
    const handleWidth = handle.offsetWidth
    const handleHeight = navItems.length * 36 + 16 // 根据图标数量和padding计算高度

    const maxX = containerRect.width - handleWidth
    const maxY = containerRect.height - handleHeight

    // 计算最终位置（相对于容器）
    const finalX = Math.max(0, Math.min(relativeX, maxX))
    const finalY = Math.max(0, Math.min(relativeY, maxY))

    // 更新位置样式
    drawerPosition.left = `${finalX}px`
    drawerPosition.top = `${finalY}px`
    drawerPosition.transform = 'none' // 清除transform，使用绝对定位

    // 根据位置判断抽屉方向
    isDrawerRight.value = finalX > containerRect.width / 2
  }

  // 结束拖拽处理函数
  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 生命周期钩子
onMounted(() => {

})

// 组件卸载时移除监听
onUnmounted(() => {
  // 移除可能的事件监听器
  document.removeEventListener('mousemove', () => {})
  document.removeEventListener('mouseup', () => {})
})
</script>

<!-- 自定义指令可以单独提到 <script> 中注册 -->
<script>
export default {
  directives: {
    focus: {
      mounted(el) {
        el.focus()
      }
    }
  }
}
</script>

<style>
.wcs-simulator-container {
  position: relative;
  width: 100%;
  height: 90vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

.simulator-view {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 登录界面 */
.login-container {
  position: fixed;
  top: 50px;
  left: 100px;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  overflow: hidden;
  pointer-events: none;
}

.login-card {
  width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  pointer-events: auto;
}

.login-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.logo-container {
  margin-bottom: 20px;
}

.logo-image {
  max-width: 180px;
  height: auto;
  margin-bottom: 10px;
}

.login-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 30px;
  font-weight: 500;
}

.login-form {
  text-align: left;
}

.login-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.login-form :deep(.el-input__inner) {
  height: 45px;
  line-height: 45px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.login-form :deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.verification-code-container {
  display: flex;
  align-items: center;
}

.verification-code-img {
  height: 38px;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 8px;
  font-size: 16px;
  margin-top: 10px;
  background: linear-gradient(90deg, #3a8ee6, #5ca9ff);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  background: linear-gradient(90deg, #5ca9ff, #3a8ee6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.button-text {
  position: relative;
  z-index: 1;
  letter-spacing: 2px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

/* 抽屉导航样式 */
.drawer-handle {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 99;
  cursor: move;
  overflow: visible;
  transition: width 0.3s ease;
  background-color: transparent;
}

.drawer-handle.drawer-expanded {
  width: auto;
}

.drawer-handle.drawer-left-expand,
.drawer-handle.drawer-right-expand {
  background-color: transparent;
}

.drawer-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
}

.drawer-icon {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
  position: relative;
  margin: 2px 0;
  cursor: pointer;
  color: #409eff;
  transition: background-color 0.2s ease;
}

.drawer-icon:hover {
  background-color: rgba(121, 64, 215, 0.05);
  border-radius: 6px;
}

.icon-wrapper {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 18px;
  height: 18px;
}

/* 展开内容（文字） */
.drawer-icon-text {
  position: absolute;
  left: 100%;
  top: 0;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.95);
  padding: 0 8px;
  height: 100%;
  line-height: 36px;
  color: #409eff;
  border-radius: 6px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateX(10px);
  pointer-events: none;
}

.drawer-handle.drawer-expanded .drawer-icon-text {
  opacity: 1;
  pointer-events: auto;
}

/* 右侧展开文字朝左 */
.drawer-handle.drawer-handle-right .drawer-icon-text {
  left: auto;
  right: 100%;
  transform: translateX(-10px);
  text-align: right;
}

/* 切换按钮样式 */
.drawer-toggle {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #409eff;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
}

/* 左侧抽屉的展开按钮在右侧 */
.drawer-handle:not(.drawer-handle-right) .drawer-toggle {
  right: -10px;
}

/* 右侧抽屉的展开按钮在左侧 */
.drawer-handle.drawer-handle-right .drawer-toggle {
  left: -10px;
}

.nav-drawer {
  background: #f0f8ff;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  padding: 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.drawer-logo {
  width: 40px;
  height: auto;
  margin-right: 15px;
}

.drawer-title {
  margin: 0;
  color: #303133;
  font-weight: 500;
}

.drawer-menu {
  border-right: none;
  background: transparent;
}

.drawer-menu .el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: 0 10px;
  border-radius: 8px;
}

.drawer-menu .el-menu-item.is-active {
  background: linear-gradient(90deg, #3a8ee6, #5ca9ff);
  color: white;
}

.drawer-menu .el-menu-item:hover {
  background-color: #ecf5ff;
}
</style>
