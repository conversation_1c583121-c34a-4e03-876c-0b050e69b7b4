// 订单状态常量定义
export const BILL_STATUS = {
  UNAUDITED: 0,      // 未审核
  AUDITED: 1,        // 已审核
  IN_OPERATION: 2,   // 操作中
  VOIDED: 3,         // 已作废
  AUTO_CLOSED: 4,    // 自动结单
  MANUAL_CLOSED: 5   // 手动结单
}

// 状态码到描述的映射
export const BILL_STATUS_MAP = {
  [BILL_STATUS.UNAUDITED]: {
    key: 'billStatus0',
    cssClass: 'status-unaudited',
    description: '未审核'
  },
  [BILL_STATUS.AUDITED]: {
    key: 'billStatus1',
    cssClass: 'status-audited',
    description: '已审核'
  },
  [BILL_STATUS.IN_OPERATION]: {
    key: 'billStatus2',
    cssClass: 'status-processing',
    description: '操作中'
  },
  [BILL_STATUS.VOIDED]: {
    key: 'billStatus3',
    cssClass: 'status-voided',
    description: '已作废'
  },
  [BILL_STATUS.AUTO_CLOSED]: {
    key: 'billStatus4',
    cssClass: 'status-auto-closed',
    description: '自动结单'
  },
  [BILL_STATUS.MANUAL_CLOSED]: {
    key: 'billStatus5',
    cssClass: 'status-manual-closed',
    description: '手动结单'
  }
}

// 获取状态配置的工具函数
export const getBillStatusConfig = (status) => {
  return BILL_STATUS_MAP[status] || BILL_STATUS_MAP[BILL_STATUS.UNAUDITED]
}

// 验证状态码是否有效
export const isValidBillStatus = (status) => {
  return status >= 0 && status <= 5 && Number.isInteger(status)
}

// 获取所有可用的状态码
export const getAllBillStatuses = () => {
  return Object.values(BILL_STATUS)
}
