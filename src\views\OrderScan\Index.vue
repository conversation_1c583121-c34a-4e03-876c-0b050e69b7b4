<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {ArrowLeft, Document} from "@element-plus/icons-vue";
import {GetOrderByNumber} from "@/api/index.js";

const router = useRouter()
const { t } = useI18n()
const orderNumber = ref('')
const orderInputRef = ref(null)

// 页面加载完成后自动聚焦到输入框
onMounted(async () => {
  await nextTick()
  if (orderInputRef.value) {
    orderInputRef.value.focus()
  }
})

const handleConfirm = async() => {
  if (!orderNumber.value.trim()) {
    ElMessage.warning(t('orderScan.orderNumberRequired'))
    return
  }

  const params=[`${orderNumber.value}`]
  const response = await GetOrderByNumber(params)
  if(response.Status){  // 这里可以添加订单验证逻辑
    ElMessage.success(t('orderScan.orderConfirmSuccess', { orderNumber: orderNumber.value }))
    orderNumber.value = ''
  }else{
    ElMessage.error(t('orderScan.orderConfirmFailed', { orderNumber: orderNumber.value }))
    orderNumber.value = ''
  }
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="order-scan-container">
    <!-- 页面标题 -->
    <div class="header">
      <el-button
        type="text"
        @click="goBack"
        class="back-btn"
        size="large"
      >
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1 class="title">{{ t('orderScan.title') }}</h1>
    </div>
    <!-- 主要内容区 -->
    <div class="content">
      <div class="form-container">
        <el-form @submit.prevent="handleConfirm">
          <el-form-item :label="t('orderScan.orderNumber')" class="order-input-item"  >
            <el-input
              ref="orderInputRef"
              v-model="orderNumber"
              :placeholder="t('orderScan.orderNumberPlaceholder')"
              size="large"
              clearable
              @keyup.enter="handleConfirm"
              class="order-input"
            >
              <template #prefix>
                <el-icon><Document /></el-icon>
              </template>
              <template #suffix>
                <svg t="1755229095243" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2446" width="16" height="16"><path d="M192 416C172.8 416 160 403.2 160 384L160 256c0-51.2 44.8-96 96-96l128 0c19.2 0 32 12.8 32 32S403.2 224 384 224L256 224C236.8 224 224 236.8 224 256l0 128C224 403.2 211.2 416 192 416z" fill="#272636" p-id="2447"></path><path d="M384 864 256 864c-51.2 0-96-44.8-96-96l0-128c0-19.2 12.8-32 32-32S224 620.8 224 640l0 128c0 19.2 12.8 32 32 32l128 0c19.2 0 32 12.8 32 32S403.2 864 384 864z" fill="#272636" p-id="2448"></path><path d="M768 864l-128 0c-19.2 0-32-12.8-32-32s12.8-32 32-32l128 0c19.2 0 32-12.8 32-32l0-128c0-19.2 12.8-32 32-32s32 12.8 32 32l0 128C864 819.2 819.2 864 768 864z" fill="#272636" p-id="2449"></path><path d="M832 416c-19.2 0-32-12.8-32-32L800 256c0-19.2-12.8-32-32-32l-128 0C620.8 224 608 211.2 608 192S620.8 160 640 160l128 0c51.2 0 96 44.8 96 96l0 128C864 403.2 851.2 416 832 416z" fill="#272636" p-id="2450"></path><path d="M832 544 192 544C172.8 544 160 531.2 160 512S172.8 480 192 480l640 0c19.2 0 32 12.8 32 32S851.2 544 832 544z" fill="#272636" p-id="2451"></path></svg>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 确认按钮 -->
      <div class="button-container">
        <el-button
          type="primary"
          size="large"
          @click="handleConfirm"
          class="confirm-btn"
          :disabled="!orderNumber.trim()"
        >
          {{ t('common.confirm') }}
        </el-button>
      </div>
    </div>
  </div>

</template>

<style scoped>
.order-scan-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out;
}

.header {
  background: white;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  animation: slideInDown 0.6s ease-out;
}

.back-btn {
  position: absolute;
  left: 20px;
  color: #409eff;
  font-size: 20px;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: scale(1.1);
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  width: 100%;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 20px;
  animation: slideInUp 0.6s ease-out;
}

.form-container {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.order-input-item {
  margin-bottom: 0;
  flex-direction: column; /* 当 label 太宽时可以垂直排列 */

}

.order-input-item :deep(.el-form-item__label) {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-top: 5px;
  justify-content: center;

}

.order-input {
  font-size: 16px;
}

.order-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.order-input :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.2);
}

.order-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

.button-container {
  display: flex;
  justify-content: center;
}

.confirm-btn {
  width: 200px;
  height: 50px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.confirm-btn:hover::before {
  left: 100%;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.confirm-btn:active {
  transform: translateY(0);
}

.confirm-btn:disabled {
  background: #c0c4cc;
  box-shadow: none;
  transform: none;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding: 30px 15px;
  }

  .form-container {
    padding: 30px 20px;
  }

  .title {
    font-size: 20px;
  }

  .confirm-btn {
    width: 180px;
    height: 45px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 15px;
  }

  .content {
    padding: 20px 10px;
  }

  .form-container {
    padding: 25px 15px;
  }

  .confirm-btn {
    width: 160px;
    height: 40px;
    font-size: 14px;
  }
}
</style>