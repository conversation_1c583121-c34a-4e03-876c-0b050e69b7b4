<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import {ElMessage} from "element-plus";
import {request} from "@/utils/request.ts";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";

const router = useRouter()

const store = useAppStore()
const userInfo = reactive({
  userName: '',
  password: '',
  verificationCode: '',
  UUID: undefined
})
// 状态变量
const hasToken = ref(false)
const loading = ref(false)
const loginFormRef = ref(null)
const codeImgSrc = ref('')


// 表单验证规则
const rules = reactive({
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  verificationCode: [
    { required: false, message: '请输入验证码', trigger: 'blur' }
  ]
})

// 回车登录
const loginPress = (e : KeyboardEvent) => {
  if (e.keyCode === 13) {
    login()
  }
}

const login = () => {
  if (!userInfo.userName) return ElMessage.error('请输入用户名')
  if (!userInfo.password) return ElMessage.error('请输入密码')

  loading.value = true
  request.post('/api/user/login', userInfo)
      .then((result) => {
        loading.value = false
        if (!result.status) {
          if (codeImgSrc.value) {
            getVierificationCode()
          }
          return ElMessage.error(result.message)
        }

        store.setUserInfo(result.data)
        hasToken.value = true
        console.log('登录成功，跳转到首页')

        // 检查是否有重定向参数，如果有则跳转到指定页面，否则跳转到首页
        const redirectPath = router.currentRoute.value.query.redirect as string
        const targetPath = redirectPath && redirectPath !== '/' ? redirectPath : '/home'

        router.push(targetPath).catch(err => {
          console.error('登录后路由跳转失败:', err)
          // 如果跳转失败，尝试跳转到首页
          router.push('/home')
        })
      })
      .catch(error => {
        loading.value = false
        console.error('登录出错:', error)
        ElMessage.error('登录失败，请稍后重试')
        if (codeImgSrc.value) {
          getVierificationCode()
        }
      })
}
// 获取验证码
const getVierificationCode = () => {
  request.get('/api/User/getVierificationCode').then((x) => {
    codeImgSrc.value = 'data:image/png;base64,' + x.img
    userInfo.UUID = x.uuid
  }).catch(err => {
    console.error('获取验证码失败:', err)
    codeImgSrc.value = ''
  })
}

onMounted(() => {
  getVierificationCode()
})
</script>



<template>
  <div class="login-container">
    <div class="login-card">
      <div class="logo-container">
        <img src="@/assets/imgs/白底logo.png" alt="Logo" class="logo-image">
      </div>

      <h2 class="login-title">用户登录</h2>
      <el-form ref="loginFormRef" :model="userInfo" :rules="rules" label-position="top" class="login-form" @keypress="loginPress">
        <el-form-item label="用户名" prop="userName">
          <el-input
              v-model="userInfo.userName"
              placeholder="请输入用户名"
              size="large"
          >

          </el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
              v-model="userInfo.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              show-password
          ></el-input>
        </el-form-item>

        <el-form-item label="验证码" prop="verificationCode" v-if="codeImgSrc">
          <div class="verification-code-container">
            <el-input
                v-model="userInfo.verificationCode"
                placeholder="请输入验证码"
                size="large"
            ></el-input>
            <img
                :src="codeImgSrc"
                class="verification-code-img"
                @click="getVierificationCode"
                alt="验证码"
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
              type="primary"
              @click="login"
              :loading="loading"
              class="login-button"
          >
            <span class="button-text">登 录</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>

</template>


<style scoped>

/* 登录容器 - 简化定位 */
.login-container {
  position: fixed;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  overflow: hidden;
  pointer-events: none;
}

/* 登录卡片 - 移除不必要的定位 */
.login-card {
  width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  pointer-events: auto;
  /* 移除 absolute 定位 */
  margin: 0 auto; /* 水平居中 */
}



.login-form :deep(.el-input__wrapper) {

  box-shadow: none;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
}

.login-form :deep(.el-input__inner) {
  height: 45px;
  line-height: 45px;
  padding: 0; /* 移除默认内边距 */
}

.login-form :deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.2);
}
</style>