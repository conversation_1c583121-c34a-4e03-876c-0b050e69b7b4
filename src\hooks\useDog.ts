import {ref,onMounted} from 'vue'
import {getExample3} from '../api/index'

export default function (){
    // 数据
    let dogList = ref<string[]>([
        'https://images.dog.ceo/breeds/pembroke/n02113023_4373.jpg'
    ])

    // 方法
    async function getDog(){
        try {
            let result = await getExample3();
            dogList.value.push(result.data)
        } catch (error) {
            alert(error)
        }
    }

    // 钩子
    onMounted(()=>{
        getDog()
    })
    // 向外部提供东西
    return {dogList,getDog}
}