<template>
  <div class="overview-container">
    <el-row :gutter="16">
      <el-col :span="24">
        <el-card shadow="never" class="header-card">
          <el-row justify="space-between" align="middle">
            <el-col :span="12">
              <div class="header-title">设备状态总览</div>
              <div class="header-subtitle">实时监控所有设备运行情况</div>
            </el-col>
            <el-col :span="12" style="text-align: right;">
              <el-statistic title="PLC设备总数" :value="plcDevices.length" />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="16">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="device in plcDevices" :key="device.Id">
        <el-card shadow="hover" class="device-card" :body-style="{ padding: '0px' }" @click="navigateToDevice(device)">
          <div class="card-header">
            <span class="device-name">{{ device.Name }}</span>
            <el-tag :type="statusMap[getDeviceStatus(device)].type" size="small" effect="dark">
              {{ statusMap[getDeviceStatus(device)].text }}
            </el-tag>
          </div>
          <div class="card-content">
            <div class="device-icon">
              <component :is="getDeviceIcon(device)" :style="{color: statusMap[getDeviceStatus(device)].color}"></component>
            </div>
            <div class="device-info">
              <div class="info-item">
                <span>类型:</span>
                <span>{{ getDeviceType(device) }}</span>
              </div>
              <div class="info-item">
                <span>模式:</span>
                <span>{{ device.Enabled === 1 ? '运行' : '离线' }}</span>
              </div>
              <div class="info-item">
                <span>IP:</span>
                <span>{{ device.Ip }}</span>
              </div>
              <div class="info-item">
                <span>端口:</span>
                <span>{{ device.Port }}</span>
              </div>
              <div class="info-item">
                <span>数量:</span>
                <span>{{ device.wcs_Stations?.length || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-divider>消息</el-divider>
    <div class="message-panel">
      <el-scrollbar height="200px" ref="messageScrollbar">
        <div v-for="(msg, index) in messages"
             :key="index"
             class="message-item"
             :class="{
                   'new-message': msg.isNew,
                   'isAnimating': msg.isAnimating
                 }">
          <div class="message-time">{{ msg.time }}</div>
          <div class="message-content">
            <el-tag size="small" :type="msg.type">{{ msg.title }}</el-tag>
            <span>{{ msg.content }}</span>
          </div>
        </div>
        <div v-if="messages.length === 0" class="empty-message">
          暂无消息
        </div>
      </el-scrollbar>
    </div>

  </div>
</template>

<script setup>
import { ref, markRaw, computed, onUnmounted, onMounted } from 'vue'
import { Cpu, Promotion, Refrigerator } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useDeviceStore } from '../../store/device';
import http from '@/api/http';
import * as signalR from '@microsoft/signalr';
const router = useRouter();
const deviceStore = useDeviceStore();

// 从store获取PLC设备列表
const plcDevices = computed(() => deviceStore.getPlcDevices);

// 获取设备状态
const getDeviceStatus = (device) => {
  return device.Enabled === 1 ? 'running' : 'offline';
};

// 获取设备类型
const getDeviceType = (device) => {
  if (device.Name.includes('堆垛机')) {
    return '堆垛机';
  } else if (device.Name.includes('输送线')) {
    return '输送线';
  } else {
    return device.Type === '1' ? 'PLC控制器' : '未知设备';
  }
};

// 获取设备图标
const getDeviceIcon = (device) => {
  if (device.Name.includes('堆垛机')) {
    return markRaw(Cpu);
  } else if (device.Name.includes('输送线')) {
    return markRaw(Promotion);
  } else if (device.Name.includes('穿梭车')) {
    return markRaw(Refrigerator);
  } else {
    return markRaw(Cpu); // 默认图标
  }
};

// 导航到设备详情页
const navigateToDevice = async (device) => {
  // 设置选中的PLC设备
  deviceStore.setSelectedPlc(device.Id);
  
  if (device.Name.includes('堆垛机')) {
    try {
      await router.push({
        name: 'device_stacker',
        query: { deviceId: device.Id }
      });
    } catch (error) {
      console.error('导航到堆垛机设备详情页失败:', error);
      ElMessage.error('导航到堆垛机设备详情页失败');
    }
  } else if (device.Name.includes('输送线')) {
    try {
      await router.push({
        name: 'device_conveyor',
        query: { deviceId: device.Id }
      });
    } catch (error) {
      console.error('导航到输送线设备详情页失败:', error);
      ElMessage.error('导航到输送线设备详情页失败');
    }
  } else {
    ElMessage.info(`${getDeviceType(device)}详情页面尚未开发`);
  }
};

const statusMap = ref({
  running: { text: '运行中', type: 'success', color: '#67c23a' },
  idle: { text: '空闲', type: 'primary', color: '#409eff' },
  error: { text: '故障', type: 'danger', color: '#f56c6c' },
  offline: { text: '离线', type: 'info', color: '#909399' },
});
// 消息列表
const messages = ref([]);
let connection = null;
const messageScrollbar = ref(null);

// 初始化SignalR连接
const initSignalR = async () => {
  try {
    // 获取当前登录用户信息
    const result = await http.post('api/user/GetCurrentUserInfo');
    if (result && result.data) {
      // 创建连接
      connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect()
        .withUrl(`${http.ipAddress}wcsHub`)
        .build();

      // 启动连接
      await connection.start();
      console.log('SignalR连接成功');

      // 自动重连成功后的处理
      connection.onreconnected((connectionId) => {
        console.log('SignalR重新连接成功:', connectionId);
        addMessage('系统', '连接已恢复', 'success');
      });

      // 接收消息回调
      connection.on('StationMsg', (message) => {
        addMessage('工位消息', message, 'info');
      });
      connection.on('StrackerMsg', (message) => {
        console.log('收到堆垛机消息:', message);
        addMessage('堆垛机消息', message, 'info');
      });
    }
  } catch (err) {
    console.error('SignalR连接失败:', err);
    addMessage('系统', '连接失败', 'danger');
  }
};

// 添加消息到列表
const addMessage = (title, content, type = 'info') => {
  const now = new Date();
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

  // 先添加一个占位消息，用于动画效果
  const newMessage = {
    time: timeStr,
    title: title,
    content: content,
    type: type,
    isNew: true,
    isAnimating: true
  };

  messages.value.unshift(newMessage);

  // 最多保留50条消息
  if (messages.value.length > 50) {
    messages.value.pop();
  }

  // 滚动到顶部并在短暂延迟后移除新消息高亮
  nextTick(() => {
    if (messageScrollbar.value) {
      messageScrollbar.value.setScrollTop(0);
    }

    // 300ms后结束入场动画
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isAnimating = false;
      }
    }, 300);

    // 2秒后移除新消息高亮
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isNew = false;
      }
    }, 2000);
  });
};
// 组件挂载时初始化SignalR连接
onMounted(() => {
  initSignalR();

  // // 定时刷新工位数据
  // timer = setInterval(() => {
  //   if (selectedStation.value) {
  //     fetchStationData();
  //   }
  // }, 10000); // 每10秒刷新一次
});

onUnmounted(() => {
  // if (timer) {
  //   clearInterval(timer);
  // }

  // 关闭SignalR连接
  if (connection) {
    connection.stop();
  }
});

</script>

<style scoped>
.overview-container {
  height: 100%;
}
.header-card {
  margin-bottom: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
}
.header-title {
  font-size: 24px;
  font-weight: 500;
}
.header-subtitle {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
}
:deep(.header-card .el-statistic__head) {
  color: #fff !important;
  opacity: 0.8;
}
:deep(.header-card .el-statistic__content) {
  color: #fff !important;
  font-size: 32px;
}
.message-panel {
  margin-top: 20px;
}
.message-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 8px 5px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.5s ease;
  opacity: 1;
  transform: translateY(0);
}
.message-item.new-message {
  background-color: rgba(64, 158, 255, 0.1);
  animation: highlight-new-message 2s ease;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.message-item.isAnimating {
  animation: slide-down 0.3s ease-out;
}
.device-card {
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s;
  cursor: pointer; /* 增加指针样式，表明可点击 */
}
.device-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}
.device-card:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.08);
  opacity: 0.95;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}
.device-name {
  font-weight: 500;
}
.card-content {
  display: flex;
  align-items: center;
  padding: 16px;
}
.device-icon {
  font-size: 48px;
  margin-right: 16px;
}
.device-info {
  flex-grow: 1;
  font-size: 13px;
}
.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #606266;
}
.info-item span:first-child {
  color: #909399;
}
.info-item:last-child {
  margin-bottom: 0;
}
</style>