export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    back: '返回',
    reset: '重置',
    submit: '提交',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息'
  },
  
  // 主页面
  index: {
    title: '奇德新材料',
    sectionTitle: '信息获取',
    scanOrder: '扫描订单',
    inStorage: '入库管理'
  },
  
  // 扫描订单页面
  orderScan: {
    title: '扫描订单',
    orderNumber: '订单号',
    orderNumberPlaceholder: '请输入或扫描订单号',
    orderNumberRequired: '请输入订单号',
    orderConfirmSuccess: '订单号：{orderNumber} 确认成功',

    orderConfirmFailed: '订单号：{orderNumber} 确认失败',
  },
  
  // 入库管理页面
  inStorage: {
    title: '入库管理',
    orderNumber: '订单号',
    orderNumberPlaceholder: '请输入订单号',
    orderNumberRequired: '请输入订单号',
    orderDetailsTitle: '订单详情',
    orderInfo: '订单号: {orderNumber}',
    reQuery: '重新查询',
    getOrderDetailsSuccess: '订单详情获取成功',
    getOrderDetailsFailed: '获取订单详情失败',
    // 订单详情字段
    availableQty: '可用数量',
    billId: '订单内部编码',
    billNo: '内部订单号',
    billStatus: '订单状态',
    detailId: '订单明细编码',
    fQty: '已上架数量',
    linkBillNoERP: 'ERP订单号',
    pQty: '已组盘数量',
    productBatch: '产品批次',
    productCode: '产品编码',
    productName: '产品名称',
    productSpec: '产品描述',
    qty: '订单总数',
    sortCode: '明细序号',
    warehouseId: '仓库编码',
    // 订单状态 (BillStatus 数值对应)
    billStatus0: '未审核',
    billStatus1: '已审核',
    billStatus2: '操作中',
    billStatus3: '已作废',
    billStatus4: '自动结单',
    billStatus5: '手动结单',
    routerMessage:'不可编辑，可用数量为0'
  },
  
  // 入库详情页面
  inStorageDetails: {
    title: '入库详情',
    productInfo: '产品信息',
    productName: '产品名称',
    productCode: '产品编码',
    productBatch: '产品批次',
    availableQty: '可用数量',
    plannedQuantity: '计划数量',
    suggestedLocation: '建议位置',
    locationScan: '托盘编号',
    locationPlaceholder: '请输入托盘编号',
    locationRequired: '请输入库位',
    locationConfirmSuccess: '库位确认成功',
    inStorageQuantity: '数量',
    quantityPlaceholder: '请输入数量',
    quantityRequired: '请输入数量',
    quantityExceedsMax: '输入数量不能超过 {max}',
    quantityMustBePositive: '输入数量必须大于0',
    inStorageSuccess: '入库操作成功，数量：{quantity}',
    inStorageError: '入库操作失败',
    operationRecord: '操作记录',
    noOperationRecord: '暂无操作记录'
  },
  
  // 语言切换
  language: {
    chinese: '中文',
    english: 'English',
    thai: 'ไทย'
  }
}
