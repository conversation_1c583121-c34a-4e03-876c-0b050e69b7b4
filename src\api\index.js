import { request } from '@/utils/request'

// 获取订单详情列表
export const getOrderById = (Number) => {
    return request.post('/api/Wms_StockInBill/GetStockInBillByStatusAndNo',Number);
}
// 入库单入库
export const StockInByBill = (data) => {
    return request.post('/api/Wms_Task/StockInByBill', data);
}

export const GetOrderByNumber=(Number) => {
    return request.post('/api/Wms_Receiving/GetOrderByNumber',Number);
}




















// === 示例 API  ===
export const postExample = () => {
    return request.post('', data);
};

export const getExample = (id)  => {
    return request.get(`/xxx/${id}`);
}

export const getExample3 = () => {
    return request.get( '/xxx');
}

export const getExample2 = (params)=> {
    if (!params) {
        return Promise.reject(new Error('params不能为空'));
    }
    return request.get('/xxx',  params );
};

export const deleteExample = (id) => {
    return request.delete(`/xxx/${id}`)
}

export const putExample = (data)  => {
    return request.put('', data)
}