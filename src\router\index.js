import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      path: '/index',
      name: 'Index',
      component: () => import('../views/Index.vue')
    },
    {
      path: '/order-scan',
      name: 'OrderScan',
      component: () => import('../views/OrderScan/Index.vue')
    },
    {
      path: '/in-storage',
      name: 'InStorage',
      component: () => import('../views/InStorage/Index.vue')

    },
    {
      path: '/in-storage/details',
      name: 'InStorageDetails',
      component: () => import('../views/InStorage/Details.vue'),
      props(route){
        return route.query
      }
    }
  ]
})

export default router