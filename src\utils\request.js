import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建Axios实例
const service = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || import.meta.env.BASE_URL || '',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json; charset=utf-8'
    },
    // // 代理服务器配置（可选）
    // proxy: {
    //   protocol: 'https',
    //   host: '127.0.0.1',
    //   port: 8888,
    //    auth: { username: 'admin', password: 'password' }
    // },
    // withCredentials: true // 跨域时是否携带凭证
})

// 请求拦截器
service.interceptors.request.use(
    (config) => {
        // 在发送请求之前做些什么，例如加入 token
        // const token = localStorage.getItem('token')
        // if (token && config.headers) {
        //   config.headers['Authorization'] = `Bearer ${token}`
        // }
        return config
    },
    (error) => Promise.reject(error)
)

// 统一错误处理
const handleError = (error) => {
    if (error.response) {
        const status = error.response.status
        const data = error.response.data || {}

        switch (status) {
            case 400:
                ElMessage.error(data.message || '请求参数错误')
                break
            case 401:
                ElMessage.error('未授权，请重新登录')
                break
            case 403:
                ElMessage.error('拒绝访问')
                break
            case 404:
                ElMessage.error('请求的资源不存在')
                break
            case 500:
                ElMessage.error(data.message || '服务器内部错误')
                break
            default:
                ElMessage.error(data.message || `请求错误 (${status})`)
        }
    } else if (error.request) {
        if (error.message.includes('timeout')) {
            ElMessage.error('请求超时，请重试')
        } else {
            ElMessage.error('网络错误，请检查您的网络连接')
        }
    } else {
        ElMessage.error(error.message || '请求配置错误')
    }

    return Promise.reject(error)
}

// 响应拦截器
service.interceptors.response.use(
    (response) => response.data,
    (error) => {
        const config = error.config || {}

        // 网络或 5xx 错误重试
        if (config.retry > 0 && (!error.response || (error.response.status >= 500 && error.response.status < 600))) {
            config.retry -= 1
            config.__retryCount = (config.__retryCount || 0) + 1

            return new Promise((resolve) => {
                setTimeout(() => {
                    console.log(`重试请求: ${config.url}, 尝试次数: ${config.__retryCount}`)
                    resolve()
                }, config.retryDelay || 1000)
            }).then(() => service(config))
        }

        return handleError(error)
    }
)

// 通用请求方法
export const request = {
    get(url, params, config) {
        return service.get(url, { params, ...config })
    },
    post(url, data, config) {
        return service.post(url, data, config)
    },
    put(url, data, config) {
        return service.put(url, data, config)
    },
    delete(url, config) {
        return service.delete(url, config)
    }
}
