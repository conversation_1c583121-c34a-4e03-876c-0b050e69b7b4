<template>
  <div class="conveyor-container">
     <el-card shadow="never">
       <template #header>
        <div class="card-header">
          <span>输送线状态监控</span>
        </div>
      </template>
      <div class="conveyor-layout">
        <div v-for="conveyor in conveyors" :key="conveyor.id" class="conveyor-item">
            <div class="conveyor-info">
                <span class="conveyor-name">{{ conveyor.name }}</span>
                <el-tag :type="statusMap[conveyor.status].type" effect="dark">{{ statusMap[conveyor.status].text }}</el-tag>
            </div>
            <div class="conveyor-belt" :class="conveyor.status">
                <div v-if="conveyor.palletCode" class="pallet" :title="'托盘号: ' + conveyor.palletCode">
                    <el-icon><Box /></el-icon>
                </div>
            </div>
             <div class="control-buttons">
                <el-button size="small" type="primary" plain @click="sendCommand(conveyor.id, 'start')">启动</el-button>
                <el-button size="small" type="danger" plain @click="sendCommand(conveyor.id, 'stop')">停止</el-button>
            </div>
        </div>
      </div>
     </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Box } from '@element-plus/icons-vue';
import { ElNotification } from 'element-plus';
import http from '../../api/http';

const conveyors = ref([]);

// 获取输送线设备列表
const fetchConveyors = async () => {
  try {
    const response = await http.post('/api/Wcs_Hardware/GetAllData', {}, true);
    if (response.status === 0 && Array.isArray(response.rows)) {
      // 过滤只获取输送线相关设备
      const conveyorDevices = response.rows.filter(device => 
        device.Name.includes('输送线') || device.Type === '2' // 假设Type=2表示输送线
      );
      
      // 将API返回的数据转换为输送线列表格式
      conveyors.value = conveyorDevices.map(device => {
        // 根据设备状态设置不同的显示状态
        let status = 'offline';
        if (device.Enabled === 1) {
          status = 'running'; // 默认为空闲状态
          // 这里可以根据其他条件判断是否为running或error状态
        }
        
        return {
          id: device.Id,
          name: device.Name,
          status: status,
          palletCode: null,
          ip: device.Ip,
          port: device.Port
        };
      });
      
      // 如果没有找到任何输送线设备，提供默认数据
      if (conveyors.value.length === 0) {
        setDefaultConveyors();
      }
    } else {
      console.error('获取输送线设备失败:', response);
      setDefaultConveyors();
    }
  } catch (error) {
    console.error('获取输送线设备出错:', error);
    setDefaultConveyors();
  }
};

// 设置默认输送线数据
const setDefaultConveyors = () => {
  conveyors.value = [
    { id: 'CV01', name: '入库口输送线', status: 'running', palletCode: '1001' },
    { id: 'CV02', name: '出库口输送线', status: 'idle', palletCode: null },
    { id: 'CV03', name: '巷道前输送线', status: 'error', palletCode: '1002' },
    { id: 'CV04', name: '接驳输送线', status: 'offline', palletCode: null },
  ];
};

onMounted(() => {
  fetchConveyors();
});

const statusMap = ref({
  running: { text: '运行中', type: 'success' },
  idle: { text: '空闲', type: 'primary' },
  error: { text: '故障', type: 'danger' },
  offline: { text: '离线', type: 'info' },
});

const sendCommand = (deviceId, command) => {
  console.log(`向 ${deviceId} 发送命令: ${command}`);
  // 此处应调用API
  ElNotification({
    title: '命令已发送',
    message: `已向 ${deviceId} 发送 [${command.toUpperCase()}] 命令。`,
    type: 'success',
  });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.conveyor-layout {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}
.conveyor-item {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;
   transition: all 0.3s;
 }
 .conveyor-item:hover {
   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
   transform: translateY(-2px);
  }
.conveyor-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.conveyor-name {
    font-weight: 500;
}
.conveyor-belt {
    height: 60px;
    background-color: #dcdfe6;
    border-radius: 4px;
    border: 2px dashed #c0c4cc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    overflow: hidden;
    position: relative;
}
.conveyor-belt.running .pallet {
    animation: move-pallet 5s linear infinite;
}
.conveyor-belt.error {
    border-color: #f56c6c;
}
.pallet {
    width: 40px;
    height: 40px;
    background-color: #f2e2b2;
    border: 1px solid #e6a23c;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #b37b00;
    font-size: 24px;
}
.control-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}
@keyframes move-pallet {
  0% { transform: translateX(-150px); }
  100% { transform: translateX(150px); }
}
</style> 