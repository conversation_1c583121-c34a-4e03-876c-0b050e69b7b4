
import {createRouter,createWebHistory} from 'vue-router'
import { create<PERSON><PERSON>, setActivePinia } from 'pinia'
import { useAppStore } from '@/store'

setActivePinia(createPinia())
const store = useAppStore()

// 第二步：创建路由器
const router = createRouter({
    history:createWebHistory(), //路由器的工作模式（稍后讲解）
    routes:[
        {
            path: '/404',
            name: '404',
            component: () => import('@/components/redirect/404.vue'),
            meta:{
                anonymous:true
            }
        }, {
            path: '/401',
            name: '401',
            component: () => import('@/components/redirect/401.vue')
        },
        {
            path:'/',
            name:'Index',
            component:()=>import('@/views/Home.vue'),
            meta: {
                keepAlive: false,

            },
            redirect: {
                name: 'storage_home'
            },
            children:[
                {
                    path: 'storage_home',
                    name: 'storage_home',
                    component: () => import('@/views/storage/Home.vue'),
                    meta: { anonymous: true }
                },
                {
                    path: 'storage_device',
                    name: 'storage_device',
                    component: () => import('@/views/storage/Device.vue'),
                    redirect: { name: 'device_overview' },
                    meta: { anonymous: true },
                    children: [
                        {
                            path: 'overview',
                            name: 'device_overview',
                            component: () => import('@/views/storage/DeviceOverview.vue'),
                            meta: { anonymous: true }
                        },
                        {
                            path: 'stacker',
                            name: 'device_stacker',
                            component: () => import('@/views/storage/StackerCrane.vue'),
                            meta: { anonymous: true }
                        },
                        {
                            path: 'conveyor',
                            name: 'device_conveyor',
                            component: () => import('@/views/storage/Conveyor.vue'),
                            meta: { anonymous: true }
                        }
                    ]
                },
                {
                    path: 'storage_history',
                    name: 'storage_history',
                    component: () => import('@/views/storage/History.vue'),
                    meta: { anonymous: true }
                },
                {
                    path: 'storage_WmsTask',
                    name: 'storage_WmsTask',
                    component: () => import('@/views/storage/WMSTask.vue'),
                    meta: { anonymous: true }
                }
            ],

        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/Login.vue'),
            meta: {
                anonymous: true
            }
        },
        {
            path: '/storage_Information',
            name: 'storage_Information',
            component: () => import('@/views/storage/Information.vue'),
            meta: {
                anonymous: true,
                fullScreen: true,
                keepAlive: false
            }
        }


    ]
})
router.beforeEach((to,from , next) => {

    // 1. 如果目标路由没有匹配到组件，跳转到 404 页面
    if (to.matched.length === 0) return next({ path: '/404' });

    //触发onloading方法
    store.onLoading( true);
    // 3. 判断是否允许匿名访问 || 已登录 || 正在访问登录页
    if ((to.hasOwnProperty('meta') && to.meta.anonymous) || store.isLogin() || to.path == '/login') {
        return next();
    }
    // 4. 其余情况跳转到登录页，并加上随机参数防止缓存
    next({ path: '/login', query: { redirect: Math.random() } });
})
router.afterEach(() => {
    store.onLoading(false);
})
router.onError((error) => {
    // const targetPath = router.currentRoute.value.matched;
    try {
        console.log(error.message);
        if (import.meta.env.NODE_ENV === 'development') {
            alert(error.message)
        }
        localStorage.setItem("route_error", error.message)
    } catch (e) {

    }
    window.location.href = '/'
});
// 暴露出去router
export default router
