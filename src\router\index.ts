
import {createRouter,createWebHistory} from 'vue-router'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useAppStore } from '@/store'

setActivePinia(createPinia())
const store = useAppStore()

// 第二步：创建路由器
const router = createRouter({
    history:createWebHistory(), //路由器的工作模式（稍后讲解）
    routes:[
        {
            path: '/404',
            name: '404',
            component: () => import('@/components/redirect/404.vue'),
            meta:{
                anonymous:true
            }
        }, {
            path: '/401',
            name: '401',
            component: () => import('@/components/redirect/401.vue'),
            meta:{
                anonymous:true
            }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/Login.vue'),
            meta: {
                anonymous: true
            }
        },
        {
            path: '/',
            redirect: () => {
                // 在路由重定向时检查登录状态
                const appStore = useAppStore()
                return appStore.isLogin() ? '/home' : '/login'
            }
        },
        {
            path:'/home',
            name:'Index',
            component:()=>import('@/views/Home.vue'),
            meta: {
                keepAlive: false,
                requiresAuth: true  // 需要登录才能访问
            },
            redirect: {
                name: 'storage_home'
            },
            children:[
                {
                    path: 'storage_home',
                    name: 'storage_home',
                    component: () => import('@/views/storage/Home.vue'),
                    meta: { requiresAuth: true }  // 需要登录才能访问
                },
                {
                    path: 'storage_device',
                    name: 'storage_device',
                    component: () => import('@/views/storage/Device.vue'),
                    redirect: { name: 'device_overview' },
                    meta: { requiresAuth: true },
                    children: [
                        {
                            path: 'overview',
                            name: 'device_overview',
                            component: () => import('@/views/storage/DeviceOverview.vue'),
                            meta: { requiresAuth: true }
                        },
                        {
                            path: 'stacker',
                            name: 'device_stacker',
                            component: () => import('@/views/storage/StackerCrane.vue'),
                            meta: { requiresAuth: true }
                        },
                        {
                            path: 'conveyor',
                            name: 'device_conveyor',
                            component: () => import('@/views/storage/Conveyor.vue'),
                            meta: { requiresAuth: true }
                        }
                    ]
                },
                {
                    path: 'storage_history',
                    name: 'storage_history',
                    component: () => import('@/views/storage/History.vue'),
                    meta: { requiresAuth: true }
                },
                {
                    path: 'storage_WmsTask',
                    name: 'storage_WmsTask',
                    component: () => import('@/views/storage/WMSTask.vue'),
                    meta: { requiresAuth: true }
                }
            ],

        },
        {
            path: '/storage_Information',
            name: 'storage_Information',
            component: () => import('@/views/storage/Information.vue'),
            meta: {
                requiresAuth: true,
                fullScreen: true,
                keepAlive: false
            }
        }


    ]
})
router.beforeEach((to,from , next) => {

    // 1. 如果目标路由没有匹配到组件，跳转到 404 页面
    if (to.matched.length === 0) return next({ path: '/404' });

    //触发onloading方法
    store.onLoading( true);

    // 2. 如果用户未登录且不是访问登录页面或允许匿名访问的页面，跳转到登录页
    const isLoggedIn = store.isLogin();
    const isLoginPage = to.path === '/login';
    const allowAnonymous = to.hasOwnProperty('meta') && to.meta.anonymous;

    if (!isLoggedIn && !isLoginPage && !allowAnonymous) {
        // 保存用户想要访问的页面，登录后可以跳转回去
        const redirectPath = to.path !== '/' ? to.fullPath : '/home';
        return next({ path: '/login', query: { redirect: redirectPath } });
    }

    // 3. 如果已登录且访问登录页，重定向到首页
    if (isLoggedIn && isLoginPage) {
        return next({ path: '/home' });
    }

    // 4. 其他情况正常通过
    next();
})
router.afterEach(() => {
    store.onLoading(false);
})
router.onError((error) => {
    // const targetPath = router.currentRoute.value.matched;
    try {
        console.log(error.message);
        if (import.meta.env.NODE_ENV === 'development') {
            alert(error.message)
        }
        localStorage.setItem("route_error", error.message)
    } catch (e) {

    }
    window.location.href = '/'
});
// 暴露出去router
export default router
