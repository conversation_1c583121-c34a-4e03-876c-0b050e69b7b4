<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElNotification, ElMessageBox } from 'element-plus';
import { Loading, VideoPlay, VideoPause } from '@element-plus/icons-vue';
import { useWarehouseStore } from '@/store/warehouse';
import { useStationStore } from '@/store/station';


const loading = ref(true);
const tasks = ref<any[]>([]);
const isSchedulerRunning = ref(true);
const isDevelopment = ref(true); // 页面开发中标志

const currentPage = ref(1);
const pageSize = ref(10);
const totalTasks = ref(0);

// 定义列结构 - 按照用户需求调整列
const columns = ref([
  {field:'TaskId',title:'任务id',type:'guid',width:110,hidden:false,readonly:true,require:true,align:'left'},
  {field:'WarehouseId',title:'所属仓库',type:'guid',bind:{ key:'Warehouse',data:[]},width:110,hidden:false,readonly:true,align:'left'},
  {field:'TaskNo',title:'任务号',type:'string',link:true,width:200,hidden:false,readonly:true,align:'left'},
  {field:'WorkNo',title:'工控指令',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'PalletCode',title:'托盘编码',type:'string',width:110,hidden:false,readonly:true,align:'left'},
  {field:'TaskType',title:'任务类型',type:'int',bind:{ key:'TaskType',data:[]},width:110,hidden:false,readonly:true,require:true,align:'left'},
  {field:'SrcZoneId',title:'起点库区',type:'guid',bind:{ key:'Zone',data:[]},width:110,hidden:false,readonly:true,align:'left'},
  {field:'DestZoneId',title:'终点库区',type:'guid',bind:{ key:'Zone',data:[]},width:110,hidden:false,readonly:true,align:'left'},
  {field:'Layer',title:'层',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'SrcPosition',title:'起始库位',type:'string',width:110,hidden:false,readonly:true,align:'left'},
  {field:'DestPosition',title:'目的库位',type:'string',width:110,hidden:false,readonly:true,align:'left'},
  {field:'SrcStation',title:'起始站台',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'DestStation',title:'目的站台',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'TaskStatus',title:'状态',type:'int',bind:{ key:'TaskStatus',data:[]},width:110,hidden:false,readonly:true,require:true,align:'left'},
  {field:'Priority',title:'优先级',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'Design',title:'工控进程',type:'int',bind:{ key:'TaskDesign',data:[]},width:110,hidden:false,readonly:true,require:true,align:'left'},
  {field:'TaskUptType',title:'任务更新方式',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'StockHeight',title:'货物高度',type:'int',width:110,hidden:false,readonly:true,require:true,align:'left'},
  {field:'StockWeight',title:'货物重量',type:'decimal',width:110,hidden:true,readonly:true,align:'left'},
  {field:'DataSource',title:'数据来源',type:'int',bind:{ key:'DataSource',data:[]},width:110,hidden:false,readonly:true,align:'left'},
  {field:'Passby',title:'通过的路径点',type:'int',width:110,hidden:false,readonly:true,align:'left'},
  {field:'CreateDate',title:'创建时间',type:'datetime',width:110,hidden:false,readonly:true,align:'left'},
  {field:'CreateUser',title:'创建用户',type:'string',width:110,hidden:true,readonly:true,align:'left'},
  {field:'StartDate',title:'任务开始时间',type:'datetime',width:110,hidden:true,readonly:true,align:'left'},
  {field:'FinishDate',title:'任务结束时间',type:'datetime',width:110,hidden:true,readonly:true,align:'left'},
  {field:'Remark',title:'备注',type:'string',width:150,hidden:false,align:'left'},
  {field:'IsSend',title:'是否下发任务',type:'byte',bind:{ key:'enable',data:[]},width:110,hidden:false,readonly:true,align:'left'},
  {field:'SendDate',title:'下发时间',type:'datetime',width:110,hidden:true,readonly:true,align:'left'},
  {field:'Feedback',title:'反馈编码',type:'int',width:110,hidden:true,readonly:true,align:'left'},
  {field:'FeedbackTime',title:'反馈时间',type:'datetime',width:110,hidden:true,readonly:true,align:'left'},
  {field:'FeedbackData',title:'反馈数据',type:'string',width:150,hidden:true,readonly:true,align:'left'},
  {field:'PreTaskNo',title:'前置任务指令编号',type:'string',width:110,hidden:true,readonly:true,align:'left'},
  {field:'LaneWay',title:'LaneWay',type:'int',width:110,hidden:true,readonly:true,align:'left'},
  {field:'LinkID',title:'外部关联ID',type:'string',width:110,hidden:true,align:'left'}
]);

// 过滤显示的列（非隐藏的）
const visibleColumns = ref(columns.value.filter(col => !col.hidden));

const mockTasks = [
  { 
    TaskId: 't1',
    WarehouseId: 'wh001',
    TaskNo: 'T001',
    WorkNo: 1001,
    PalletCode: 'P001',
    TaskType: 1, // 入库
    SrcZoneId: 'zone001',
    DestZoneId: 'zone002',
    Layer: 2,
    SrcPosition: 'A1-1-1',
    DestPosition: 'A1-1-2',
    SrcStation: 5,
    DestStation: 8,
    TaskStatus: 1, // 执行中
    Priority: 1,
    Design: 2,
    TaskUptType: 1,
    StockHeight: 120,
    DataSource: 1,
    Passby: 3,
    CreateDate: '2025-06-25 17:44:12',
    Remark: '',
    IsSend: 1
  },
  {
    TaskId: 't2',
    WarehouseId: 'wh001',
    TaskNo: 'T002',
    WorkNo: 1002,
    PalletCode: 'P002',
    TaskType: 2, // 出库
    SrcZoneId: 'zone003',
    DestZoneId: 'zone004',
    Layer: 1,
    SrcPosition: 'A1-1-1',
    DestPosition: 'A1-1-2',
    SrcStation: 3,
    DestStation: 6,
    TaskStatus: 0, // 新建
    Priority: 2,
    Design: 1,
    TaskUptType: 1,
    StockHeight: 90,
    DataSource: 2,
    Passby: 2,
    CreateDate: '2025-06-25 17:45:22',
    Remark: '',
    IsSend: 0
  }
];

const statusMap = ref({
  0: { text: '新建', type: 'primary' },
  1: { text: '执行中', type: 'success' },
  2: { text: '完成', type: 'info' },
  3: { text: '异常', type: 'danger' },
});

const taskTypeMap = ref({
  1: { text: '入库', type: '' },
  2: { text: '出库', type: 'warning' },
  3: { text: '移库', type: 'success' },
  4: { text: '盘点', type: 'info' },
});

// 初始化 store
const warehouseStore = useWarehouseStore();
const stationStore = useStationStore();
const fetchTasks = () => {
  loading.value = true;
  // 模拟API调用
  setTimeout(() => {
    totalTasks.value = mockTasks.length;
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    tasks.value = mockTasks.slice(start, end);
    loading.value = false;
  }, 500);
}

const startScheduler = () => {
  console.log('启动调度器');
  isSchedulerRunning.value = true;
  ElNotification.success('调度器已启动');
};

const stopScheduler = () => {
  console.log('停止调度器');
  isSchedulerRunning.value = false;
  ElNotification.warning('调度器已停止');
};

const cancelTask = (row: any) => {
   ElMessageBox.confirm(
    `确定要取消任务 [${row.TaskNo}] 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    console.log(`取消任务: ${row.TaskNo}`);
    ElNotification.success(`任务 [${row.TaskNo}] 已取消`);
    fetchTasks();
  }).catch(() => { /* a */ });
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchTasks();
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchTasks();
}

// 格式化仓库显示
const formatWarehouse = (row: any): string => {
  return warehouseStore.getWarehouseName(row.WarehouseId);
}

// 格式化库区显示
const formatZone = (zoneId: string): string => {
  return warehouseStore.getZoneName(zoneId);
}

// 格式化站台显示
const formatStation = (stationNo: number): string => {
  return stationStore.getStationNameByNo(stationNo);
}

onMounted(async () => {
  // 先加载仓库和站台数据
  await Promise.all([
    warehouseStore.fetchWarehouseList(),
    stationStore.fetchStationList()
  ]);
  
  // 然后加载任务数据
  fetchTasks();
});

</script>

<template>
  <div class="task-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>WCS 任务列表</span>
          <div class="toolbar">
            <el-tag type="success" size="large" v-if="isSchedulerRunning">
              <el-icon class="is-loading"><Loading /></el-icon>
              调度器运行中
            </el-tag>
            <el-tag type="info" size="large" v-else>
              <el-icon><VideoPause /></el-icon>
              调度器已停止
            </el-tag>
            <el-divider direction="vertical" />
            <el-button type="primary" :icon="VideoPlay" @click="startScheduler" :disabled="isSchedulerRunning">启动调度</el-button>
            <el-button type="danger" :icon="VideoPause" @click="stopScheduler" :disabled="!isSchedulerRunning">停止调度</el-button>
          </div>
        </div>
      </template>

      <el-alert
        v-if="isDevelopment"
        title="开发中页面"
        description="此页面功能正在开发中，当前显示的是模拟数据，并非真实业务数据。"
        type="warning"
        :closable="false"
        show-icon
        class="development-alert"
      />

      <el-table :data="tasks" stripe style="width: 100%" v-loading="loading">
        <el-table-column type="index" label="#" width="50" />
        
        <!-- 动态渲染可见列 -->
        <template v-for="col in visibleColumns" :key="col.field">
          <!-- 任务类型列 -->
          <el-table-column 
            v-if="col.field === 'TaskType'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              <el-tag :type="taskTypeMap[scope.row.TaskType]?.type">
                {{ taskTypeMap[scope.row.TaskType]?.text }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 状态列 -->
          <el-table-column 
            v-else-if="col.field === 'TaskStatus'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              <el-tag :type="statusMap[scope.row.TaskStatus]?.type" size="small">
                {{ statusMap[scope.row.TaskStatus]?.text }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 任务号（带链接） -->
          <el-table-column 
            v-else-if="col.field === 'TaskNo' && col.link"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              <el-link type="primary">{{ scope.row.TaskNo }}</el-link>
            </template>
          </el-table-column>
          
          <!-- 是否下发任务 -->
          <el-table-column 
            v-else-if="col.field === 'IsSend'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              <el-tag :type="scope.row.IsSend ? 'success' : 'info'" size="small">
                {{ scope.row.IsSend ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <!-- 仓库名称 -->
          <el-table-column 
            v-else-if="col.field === 'WarehouseId'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              {{ formatWarehouse(scope.row) }}
            </template>
          </el-table-column>
          
          <!-- 起点库区 -->
          <el-table-column 
            v-else-if="col.field === 'SrcZoneId'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              {{ formatZone(scope.row.SrcZoneId) }}
            </template>
          </el-table-column>
          
          <!-- 终点库区 -->
          <el-table-column 
            v-else-if="col.field === 'DestZoneId'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              {{ formatZone(scope.row.DestZoneId) }}
            </template>
          </el-table-column>
          
          <!-- 起始站台 -->
          <el-table-column 
            v-else-if="col.field === 'SrcStation'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              {{ formatStation(scope.row.SrcStation) }}
            </template>
          </el-table-column>
          
          <!-- 目的站台 -->
          <el-table-column 
            v-else-if="col.field === 'DestStation'"
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align">
            <template #default="scope">
              {{ formatStation(scope.row.DestStation) }}
            </template>
          </el-table-column>
          
          <!-- 默认列 -->
          <el-table-column 
            v-else
            :prop="col.field" 
            :label="col.title" 
            :width="col.width.toString()"
            :align="col.align" />
        </template>
        
        <!-- 操作列 -->
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button 
              size="small" 
              type="danger" 
              plain 
              :disabled="scope.row.TaskStatus !== 0" 
              @click="cancelTask(scope.row)">取消</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTasks"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
    </el-card>
  </div>
</template>

<style scoped lang="less">
.task-container {
  padding: 16px;
  background-color: #f0f2f5;
  height: 100%;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
}
.pagination {
  margin-top: 20px;
  justify-content: flex-end;
}
.development-alert {
  margin-bottom: 20px;
}
</style>