<script setup >
import { ref, reactive, onMounted } from 'vue';
import { Search, Delete, RefreshRight } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import http from '../../api/http';
import { useWarehouseStore } from '../../store/warehouse';
import { useStationStore } from '../../store/station';

// 初始化 store
const warehouseStore = useWarehouseStore();
const stationStore = useStationStore();

// 定义历史记录类型
const historyTypes = [
  { value: 'wms', label: 'WCS任务' },
  { value: 'device', label: '设备运行指令' },
  { value: 'error', label: '故障信息' }
];

// 状态映射
const statusMap = {
  0: { text: '等待中', type: 'info' },
  1: { text: '执行中', type: 'primary' },
  2: { text: '已完成', type: 'success' },
  3: { text: '异常', type: 'danger' },
  4: { text: '已取消', type: 'warning' }
};

// 分页设置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 查询表单
const queryForm = reactive({
  type: 'wms',
  keyword: '',
  status: '',
  dateRange: [],
  startTime: '',
  endTime: ''
});

// 表格加载状态
const loading = ref(false);
// 表格数据
const tableData = ref([]);
// 选中的行
const selectedRows = ref([]);
// 表单引用
const queryFormRef = ref();

// 获取历史记录数据
const fetchHistoryData = async () => {
  loading.value = true;
  
  try {
    // 处理日期范围
    if (queryForm.dateRange && queryForm.dateRange.length === 2) {
      queryForm.startTime = queryForm.dateRange[0];
      queryForm.endTime = queryForm.dateRange[1];
    } else {
      queryForm.startTime = '';
      queryForm.endTime = '';
    }
    
    // 构建查询参数
    const params = {
      // type: queryForm.type,
      // keyword: queryForm.keyword,
      // status: queryForm.status,
      // startTime: queryForm.startTime,
      // endTime: queryForm.endTime,
      // page: pagination.currentPage,
      // pageSize: pagination.pageSize
    };
    
    // 根据不同类型调用不同的API
    let apiUrl = '';
    if (queryForm.type === 'wms') {
      apiUrl = '/api/Wms_TaskHistory/GetAllData';
    } else if (queryForm.type === 'device') {
      apiUrl = '/api/Wcs_Command/GetHistoryData';
    } else if (queryForm.type === 'error') {
      apiUrl = '/api/Wcs_Error/GetHistoryData';
    }
    
    const response = await http.post(apiUrl, params, true);
    
    if (response.status === 0) {
      tableData.value = response.rows || [];
      pagination.total = response.total || 0;
    } else {
      ElMessage.error(response.msg || '获取历史数据失败');
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取历史数据出错:', error);
    ElMessage.error('请求出错，请稍后重试');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 重置查询表单
const resetForm = (formEl) => {
  if (!formEl) return;
  formEl.resetFields();
  queryForm.dateRange = [];
  pagination.currentPage = 1;
  fetchHistoryData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  fetchHistoryData();
};

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchHistoryData();
};

// 删除历史记录
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这条历史记录吗？此操作不可恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;

      // 根据不同类型调用不同的删除API
      let apiUrl = '';
      let params = {};
      if (queryForm.type === 'wms') {
        apiUrl = '/api/Wms_Task/DeleteHistory';
        params = { taskId: row.TaskId };
      } else if (queryForm.type === 'device') {
        apiUrl = '/api/Wcs_Command/DeleteHistory';
        params = { commandId: row.CommandId };
      } else if (queryForm.type === 'error') {
        apiUrl = '/api/Wcs_Error/DeleteHistory';
        params = { errorId: row.ErrorId };
      }

      const response = await http.post(apiUrl, params, true);

      if (response.status === 0) {
        ElMessage.success('删除成功');
        fetchHistoryData();
      } else {
        ElMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除历史记录出错:', error);
      ElMessage.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  }).catch(() => {});
};

// 批量删除历史记录
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录');
    return;
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条历史记录吗？此操作不可恢复`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;

      // 根据不同类型调用不同的批量删除API
      let apiUrl = '';
      let params = {};
      if (queryForm.type === 'wms') {
        apiUrl = '/api/Wms_Task/BatchDeleteHistory';
        params = { taskIds: selectedRows.value.map(row => row.TaskId) };
      } else if (queryForm.type === 'device') {
        apiUrl = '/api/Wcs_Command/BatchDeleteHistory';
        params = { commandIds: selectedRows.value.map(row => row.CommandId) };
      } else if (queryForm.type === 'error') {
        apiUrl = '/api/Wcs_Error/BatchDeleteHistory';
        params = { errorIds: selectedRows.value.map(row => row.ErrorId) };
      }

      const response = await http.post(apiUrl, params, true);

      if (response.status === 0) {
        ElMessage.success('批量删除成功');
        selectedRows.value = [];
        fetchHistoryData();
      } else {
        ElMessage.error(response.msg || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除历史记录出错:', error);
      ElMessage.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  }).catch(() => {});
};

// 选择变化处理
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};

// 根据历史类型获取状态选项
const getStatusOptions = () => {
  if (queryForm.type === 'wms') {
    return [
      { value: '0', label: '等待中' },
      { value: '1', label: '执行中' },
      { value: '2', label: '已完成' },
      { value: '3', label: '异常' },
      { value: '4', label: '已取消' }
    ];
  } else if (queryForm.type === 'device') {
    return [
      { value: '0', label: '待执行' },
      { value: '1', label: '执行中' },
      { value: '2', label: '已执行' },
      { value: '3', label: '执行失败' }
    ];
  } else if (queryForm.type === 'error') {
    return [
      { value: '0', label: '未处理' },
      { value: '1', label: '已处理' },
      { value: '2', label: '已忽略' }
    ];
  }
  return [];
};

// 格式化仓库显示
const formatWarehouse = (warehouseId) => {
  return warehouseStore.getWarehouseName(warehouseId);
}

// 格式化库区显示
const formatZone = (zoneId) => {
  return warehouseStore.getZoneName(zoneId);
}

// 格式化站台显示
const formatStation = (stationNo) => {
  return stationStore.getStationNameByNo(stationNo);
}

// 获取当前类型的表格列配置
const getColumns = () => {
  if (queryForm.type === 'wms') {
    return [
      // { prop: 'TaskId', label: '任务ID', width: '100' },
      { prop: 'WarehouseId', label: '所属仓库', width: '120', formatter: (row) => formatWarehouse(row.WarehouseId) },
      { prop: 'TaskNo', label: '任务号', width: '120' },
      { prop: 'WorkNo', label: '工控指令', width: '100' },
      { prop: 'PalletCode', label: '托盘号', width: '120' },
      { prop: 'TaskType', label: '任务类型', width: '100' },
      { prop: 'SrcZoneId', label: '起点库区', width: '120', formatter: (row) => formatZone(row.SrcZoneId) },
      { prop: 'DestZoneId', label: '终点库区', width: '120', formatter: (row) => formatZone(row.DestZoneId) },
      { prop: 'Layer', label: '层', width: '80' },
      { prop: 'SrcPosition', label: '起始库位', width: '120' },
      { prop: 'DestPosition', label: '目的库位', width: '120' },
      { prop: 'SrcStation', label: '起始站台', width: '100', formatter: (row) => formatStation(row.SrcStation) },
      { prop: 'DestStation', label: '目的站台', width: '100', formatter: (row) => formatStation(row.DestStation) },
      { prop: 'TaskStatus', label: '状态', width: '100', formatter: formatStatus },
      { prop: 'Priority', label: '优先级', width: '80' },
      { prop: 'Design', label: '工控进程', width: '100' },
      { prop: 'TaskUptType', label: '任务更新方式', width: '120' },
      { prop: 'StockHeight', label: '货物高度', width: '100' },
      { prop: 'DataSource', label: '数据来源', width: '100' },
      { prop: 'Passby', label: '通过的路径点', width: '120' },
      { prop: 'CreateDate', label: '创建时间', width: '180' },
      { prop: 'Remark', label: '备注', width: '150' },
      { prop: 'IsSend', label: '是否下发任务', width: '120', formatter: (row) => {
        return row.IsSend ? '是' : '否';
      }}
    ];
  } else if (queryForm.type === 'device') {
    return [
      { prop: 'CommandId', label: '指令ID', width: '100' },
      { prop: 'DeviceName', label: '设备名称', width: '150' },
      { prop: 'CommandType', label: '指令类型', width: '120' },
      { prop: 'Status', label: '状态', width: '100', formatter: formatStatus },
      { prop: 'Content', label: '指令内容' },
      { prop: 'CreateTime', label: '创建时间', width: '180' },
      { prop: 'ExecuteTime', label: '执行时间', width: '180' }
    ];
  } else if (queryForm.type === 'error') {
    return [
      { prop: 'ErrorId', label: '故障ID', width: '100' },
      { prop: 'DeviceName', label: '设备名称', width: '150' },
      { prop: 'ErrorCode', label: '故障代码', width: '100' },
      { prop: 'ErrorType', label: '故障类型', width: '120' },
      { prop: 'Status', label: '状态', width: '100', formatter: formatStatus },
      { prop: 'Description', label: '故障描述' },
      { prop: 'CreateTime', label: '发生时间', width: '180' },
      { prop: 'ResolveTime', label: '解决时间', width: '180' }
    ];
  }
  return [];
};

// 格式化状态显示
const formatStatus = (row) => {
  const status = row.Status !== undefined ? row.Status : (row.TaskStatus !== undefined ? row.TaskStatus : '');
  const statusInfo = statusMap[status] || { text: '未知', type: 'info' };
  return `<el-tag size="small" type="${statusInfo.type}">${statusInfo.text}</el-tag>`;
};

// 刷新数据
const refreshData = () => {
  pagination.currentPage = 1;
  fetchHistoryData();
};

// 示例数据，用于API未准备好时测试
const getExampleData = () => {
  if (queryForm.type === 'wms') {
    return [
      {
        TaskId: 'T001',
        WarehouseId: 'WH001',
        TaskNo: 'TN001',
        WorkNo: 101,
        PalletCode: 'P1001',
        TaskType: 1,
        SrcZoneId: 'SZ001',
        DestZoneId: 'DZ001',
        Layer: 2,
        SrcPosition: 'SP001',
        DestPosition: 'DP001',
        SrcStation: 1,
        DestStation: 2,
        TaskStatus: 2,
        Priority: 1,
        Design: 1,
        TaskUptType: 1,
        StockHeight: 120,
        DataSource: 1,
        Passby: 3,
        CreateTime: '2024-10-10 10:00:00',
        CompleteTime: '2024-10-10 10:05:30',
        Remark: '正常入库',
        IsSend: 1
      },
      {
        TaskId: 'T002',
        WarehouseId: 'WH001',
        TaskNo: 'TN002',
        WorkNo: 102,
        PalletCode: 'P1002',
        TaskType: 2,
        SrcZoneId: 'SZ002',
        DestZoneId: 'DZ002',
        Layer: 3,
        SrcPosition: 'SP002',
        DestPosition: 'DP002',
        SrcStation: 2,
        DestStation: 3,
        TaskStatus: 2,
        Priority: 2,
        Design: 2,
        TaskUptType: 2,
        StockHeight: 150,
        DataSource: 2,
        Passby: 4,
        CreateTime: '2024-10-10 11:00:00',
        CompleteTime: '2024-10-10 11:03:45',
        Remark: '正常出库',
        IsSend: 1
      },
      {
        TaskId: 'T003',
        WarehouseId: 'WH002',
        TaskNo: 'TN003',
        WorkNo: 103,
        PalletCode: 'P1003',
        TaskType: 3,
        SrcZoneId: 'SZ003',
        DestZoneId: 'DZ003',
        Layer: 1,
        SrcPosition: 'SP003',
        DestPosition: 'DP003',
        SrcStation: 3,
        DestStation: 4,
        TaskStatus: 3,
        Priority: 1,
        Design: 1,
        TaskUptType: 1,
        StockHeight: 100,
        DataSource: 1,
        Passby: 2,
        CreateTime: '2024-10-10 12:00:00',
        CompleteTime: null,
        Remark: '托盘不存在',
        IsSend: 1
      },
      {
        TaskId: 'T004',
        WarehouseId: 'WH002',
        TaskNo: 'TN004',
        WorkNo: 104,
        PalletCode: 'P1004',
        TaskType: 1,
        SrcZoneId: 'SZ004',
        DestZoneId: 'DZ004',
        Layer: 2,
        SrcPosition: 'SP004',
        DestPosition: 'DP004',
        SrcStation: 4,
        DestStation: 5,
        TaskStatus: 1,
        Priority: 3,
        Design: 2,
        TaskUptType: 2,
        StockHeight: 130,
        DataSource: 2,
        Passby: 3,
        CreateTime: '2024-10-10 13:00:00',
        CompleteTime: null,
        Remark: '执行中',
        IsSend: 1
      },
      {
        TaskId: 'T005',
        WarehouseId: 'WH001',
        TaskNo: 'TN005',
        WorkNo: 105,
        PalletCode: 'P1005',
        TaskType: 2,
        SrcZoneId: 'SZ005',
        DestZoneId: 'DZ005',
        Layer: 3,
        SrcPosition: 'SP005',
        DestPosition: 'DP005',
        SrcStation: 5,
        DestStation: 6,
        TaskStatus: 0,
        Priority: 2,
        Design: 1,
        TaskUptType: 1,
        StockHeight: 110,
        DataSource: 1,
        Passby: 2,
        CreateTime: '2024-10-10 14:00:00',
        CompleteTime: null,
        Remark: '等待设备响应',
        IsSend: 0
      },
      {
        TaskId: 'T006',
        WarehouseId: 'WH001',
        TaskNo: 'TN006',
        WorkNo: 106,
        PalletCode: 'P1006',
        TaskType: 4,
        SrcZoneId: 'SZ006',
        DestZoneId: 'DZ006',
        Layer: 1,
        SrcPosition: 'SP006',
        DestPosition: 'DP006',
        SrcStation: 6,
        DestStation: 7,
        TaskStatus: 4,
        Priority: 1,
        Design: 2,
        TaskUptType: 2,
        StockHeight: 90,
        DataSource: 2,
        Passby: 1,
        CreateTime: '2024-10-10 15:00:00',
        CompleteTime: '2024-10-10 15:01:00',
        Remark: '手动取消',
        IsSend: 1
      }
    ];
  } else if (queryForm.type === 'device') {
    return [
      { CommandId: 'C001', DeviceName: '1号堆垛机', CommandType: '移动', Status: 2, Content: '移动至 1-10-3', CreateTime: '2024-10-10 10:00:00', ExecuteTime: '2024-10-10 10:01:30' },
      { CommandId: 'C002', DeviceName: '入库输送线', CommandType: '启动', Status: 2, Content: '启动输送', CreateTime: '2024-10-10 10:02:00', ExecuteTime: '2024-10-10 10:02:05' },
      { CommandId: 'C003', DeviceName: '2号堆垛机', CommandType: '取货', Status: 3, Content: '从 2-5-1 取货', CreateTime: '2024-10-10 10:10:00', ExecuteTime: '2024-10-10 10:10:30' },
      { CommandId: 'C004', DeviceName: '出库输送线', CommandType: '停止', Status: 2, Content: '停止输送', CreateTime: '2024-10-10 10:15:00', ExecuteTime: '2024-10-10 10:15:03' },
      { CommandId: 'C005', DeviceName: '1号堆垛机', CommandType: '复位', Status: 1, Content: '复位至初始位置', CreateTime: '2024-10-10 10:20:00', ExecuteTime: null },
      { CommandId: 'C006', DeviceName: '巷道输送线', CommandType: '启动', Status: 0, Content: '启动输送', CreateTime: '2024-10-10 10:25:00', ExecuteTime: null },
    ];
  } else if (queryForm.type === 'error') {
    return [
      { ErrorId: 'E001', DeviceName: '1号堆垛机', ErrorCode: 'E01', ErrorType: '硬件故障', Status: 1, Description: '电机过热', CreateTime: '2024-10-10 09:30:00', ResolveTime: '2024-10-10 10:15:00' },
      { ErrorId: 'E002', DeviceName: '入库输送线', ErrorCode: 'E02', ErrorType: '通讯故障', Status: 1, Description: '通讯中断', CreateTime: '2024-10-10 11:20:00', ResolveTime: '2024-10-10 11:30:00' },
      { ErrorId: 'E003', DeviceName: '2号堆垛机', ErrorCode: 'E03', ErrorType: '硬件故障', Status: 0, Description: '定位误差过大', CreateTime: '2024-10-10 12:15:00', ResolveTime: null },
      { ErrorId: 'E004', DeviceName: '出库输送线', ErrorCode: 'E04', ErrorType: '软件故障', Status: 2, Description: '控制系统异常', CreateTime: '2024-10-10 13:40:00', ResolveTime: '2024-10-10 13:45:00' },
      { ErrorId: 'E005', DeviceName: 'WMS系统', ErrorCode: 'E05', ErrorType: '软件故障', Status: 0, Description: '数据库连接失败', CreateTime: '2024-10-10 14:30:00', ResolveTime: null },
      { ErrorId: 'E006', DeviceName: '穿梭车', ErrorCode: 'E06', ErrorType: '硬件故障', Status: 1, Description: '电池电量低', CreateTime: '2024-10-10 15:10:00', ResolveTime: '2024-10-10 15:40:00' },
    ];
  }
  return [];
};

// 首次加载获取数据
onMounted(async () => {
  // 先加载仓库和站台数据
  await Promise.all([
    warehouseStore.fetchWarehouseList(),
    stationStore.fetchStationList()
  ]);

  // 尝试获取真实数据，如果API不可用，则使用示例数据
  fetchHistoryData().catch(() => {
    const allExampleData = getExampleData();
    // 应用分页逻辑到示例数据
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    tableData.value = allExampleData.slice(startIndex, endIndex);
    pagination.total = allExampleData.length;
  });
});
</script>

<template>
  <div class="history-container">
    <el-card shadow="never" class="query-card">
      <template #header>
        <div class="card-header">
          <span>历史记录查询</span>
        </div>
      </template>

      <el-form ref="queryFormRef" :model="queryForm" label-width="80px" :inline="true">
        <el-form-item label="记录类型">
          <el-radio-group v-model="queryForm.type" @change="refreshData">
            <el-radio-button v-for="item in historyTypes" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input v-model="queryForm.keyword" placeholder="请输入关键词" clearable />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in getStatusOptions()"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="fetchHistoryData">查询</el-button>
          <el-button :icon="RefreshRight" @click="resetForm(queryFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-alert
      v-if="true"
      title="开发中页面"
      description="此页面功能正在开发中，当前显示的是模拟数据，并非真实业务数据。"
      type="warning"
      :closable="false"
      show-icon
      class="development-alert"
    />
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="card-header">
          <span>{{ historyTypes.find(item => item.value === queryForm.type)?.label || '历史记录' }}列表</span>
          <div class="header-operations">
            <el-button type="danger" :icon="Delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete">批量删除</el-button>
            <el-button type="primary" :icon="RefreshRight" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <template v-for="column in getColumns()" :key="column.prop">
          <el-table-column
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            show-overflow-tooltip
          >
            <template #default="scope" v-if="column.formatter">
              <div v-if="column.prop === 'WarehouseId' || column.prop === 'SrcZoneId' || column.prop === 'DestZoneId' || column.prop === 'SrcStation' || column.prop === 'DestStation'">
                {{ column.formatter(scope.row) }}
              </div>
              <div v-else v-html="column.formatter(scope.row)"></div>
            </template>
          </el-table-column>
        </template>
        
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button type="danger" size="small" :icon="Delete" circle @click="handleDelete(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="less">
.history-container {
  height: 100%;
  padding: 16px;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.query-card {
  margin-bottom: 0;
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.header-operations {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  flex: 1;
}

:deep(.el-tag) {
  margin-right: 0;
}
</style>