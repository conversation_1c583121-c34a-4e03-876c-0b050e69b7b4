<script setup>
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'



const router = useRouter()
const { t } = useI18n()

const navigateToOrderScan = () => {
  router.push('/order-scan')
}

const navigateToInStorage = () => {
  router.push('/in-storage')
}
</script>

<template>
  <LanguageSwitcher />
  <div class="index-container">
    <!-- 页面标题 -->
    <div class="header">
      <h1 class="title">{{ t('index.title') }}</h1>
    </div>

    <!-- 功能区 -->
    <div class="content">
      <div class="section-title">{{ t('index.sectionTitle') }}</div>
      <div class="function-grid">
        <div class="function-item" @click="navigateToOrderScan">
          <div class="icon-container scan-icon">
            <el-icon size="40">
              <svg t="1755141068973" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6276" width="32" height="32"><path d="M885.312 885.312V624H960V960H624v-74.688h261.312zM64 885.312V624h74.688v261.312H400V960H64v-74.688zM960 138.688V400h-74.688V138.688H624V64H960v74.688z m-821.312 0V400H64V64h336v74.688H138.688zM64 474.688h896v74.624H64V474.688z" fill="#262626" p-id="6277"></path></svg>
            </el-icon>

          </div>
          <div class="function-text">{{ t('index.scanOrder') }}</div>
        </div>

        <div class="function-item" @click="navigateToInStorage">
          <div class="icon-container storage-icon">
            <el-icon size="40">
              <svg t="1755141120882" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9287" width="32" height="32"><path d="M842.885223 80.406666h-82.127746v44.877008h82.127746c20.938792 0 37.973227 17.035651 37.973226 37.974443v696.943724c0 20.938792-17.034434 37.973227-37.973226 37.973227H181.112345c-20.938792 0-37.973227-17.034434-37.973227-37.973227V163.256901c0-20.938792 17.034434-37.974443 37.973227-37.974444h82.127746V80.406666H181.112345c-45.757617 0-82.850235 37.093834-82.850235 82.851451v696.943724c0 45.757617 37.093834 82.851451 82.850235 82.851451h661.772878c45.757617 0 82.850235-37.093834 82.850234-82.851451V163.256901c0-45.757617-37.092618-82.850235-82.850234-82.850235z" fill="#4E4D4D" p-id="9288"></path><path d="M511.998784 684.306254L354.658019 526.966706l31.693414-31.693414 125.647351 125.646135 125.64735-125.646135 31.692198 31.693414zM261.757408 754.623621h500.485184v44.821057H261.757408z" fill="#4E4D4D" p-id="9289"></path><path d="M489.588255 206.004148h44.821057v453.356785h-44.821057z" fill="#4E4D4D" p-id="9290"></path></svg>
            </el-icon>
          </div>
          <div class="function-text">{{ t('index.inStorage') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.index-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: slideInDown 0.6s ease-out;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #ecf0f1;
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 30px;
}

.function-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.function-item:nth-child(1) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.function-item:nth-child(2) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.function-item:hover::before {
  left: 100%;
}

.function-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.function-item:active {
  transform: translateY(-2px) scale(0.98);
}

.icon-container {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin: 0 auto 15px;
  backdrop-filter: blur(10px);
}

.function-text {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
}

/* 动画效果 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .index-container {
    padding: 15px;
  }

  .title {
    font-size: 20px;
  }

  .function-grid {
    gap: 15px;
  }

  .function-item {
    padding: 25px 15px;
  }

  .function-text {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .index-container {
    padding: 10px;
  }

  .header {
    padding: 15px;
  }

  .content {
    padding: 15px;
  }

  .function-item {
    padding: 20px 10px;
  }

  .icon-container {
    width: 50px;
    height: 50px;
  }
}
</style>