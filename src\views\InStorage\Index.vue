<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {ArrowLeft, ArrowRight} from "@element-plus/icons-vue";
import {getOrderById} from "@/api/index.js";

const router = useRouter()
const { t } = useI18n()
const orderNumber = ref('')
const orderInputRef = ref(null)
const showOrderDetails = ref(false)
const orderDetails = ref([])
const loading = ref(false)


// 页面加载完成后自动聚焦到输入框
onMounted(async () => {
  await nextTick()
  if (orderInputRef.value) {
    orderInputRef.value.focus()
  }
})



const handleConfirm = async () => {
  if (!orderNumber.value.trim()) {
    ElMessage.warning(t('inStorage.orderNumberRequired'))
    return
  }

  loading.value = true

  try {

    const FBillNos=[`${orderNumber.value}`]
    const response = await getOrderById(FBillNos)


    if(response.Status === true) {
      orderDetails.value = response.Data
      showOrderDetails.value = true
    } else {
      ElMessage.error(t('inStorage.getOrderDetailsFailed'))
      return
    }

    ElMessage.success(t('inStorage.getOrderDetailsSuccess'))
  } catch (error) {
    ElMessage.error(t('inStorage.getOrderDetailsFailed'))
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

const handleItemClick = (item) => {
  if (item.AvailableQty === 0) {
    ElMessage({
      message: `${t('inStorage.routerMessage')}`,
      type: 'warning',
    })
    return
  }
  // 跳转到详情页面，传递产品信息（使用新的数据格式）
  router.push({
    name: 'InStorageDetails',
    query: {
      billId: item.BillId,
      detailId: item.DetailId,
      productName: item.ProductName,
      productCode: item.ProductCode,
      productSpec: item.ProductSpec,
      availableQty: item.AvailableQty,
      totalQty: item.Qty,
      SortCode: item.SortCode,
      productBatch: item.ProductBatch,
      billNo: item.BillNo,
      linkBillNoERP: item.LinkBillNoERP,
      warehouseId: item.WarehouseId,
      weight:item.ProductWeight,
    }
  })
}

const goBack = () => {
  router.back()
}

const resetForm = () => {
  orderNumber.value = ''
  showOrderDetails.value = false
  orderDetails.value = []
  nextTick(() => {
    if (orderInputRef.value) {
      orderInputRef.value.focus()
    }
  })
}

// 获取状态样式类（基于数值状态码）
const getStatusClass = (status) => {
  switch (status) {
    case 0: // 未审核
      return 'status-unaudited'
    case 1: // 已审核
      return 'status-audited'
    case 2: // 操作中
      return 'status-processing'
    case 3: // 已作废
      return 'status-voided'
    case 4: // 自动结单
      return 'status-auto-closed'
    case 5: // 手动结单
      return 'status-manual-closed'
    default:
      return 'status-unaudited'
  }
}

// 获取状态文本（基于数值状态码）
const getStatusText = (status) => {
  switch (status) {
    case 0: // 未审核
      return t('inStorage.billStatus0')
    case 1: // 已审核
      return t('inStorage.billStatus1')
    case 2: // 操作中
      return t('inStorage.billStatus2')
    case 3: // 已作废
      return t('inStorage.billStatus3')
    case 4: // 自动结单
      return t('inStorage.billStatus4')
    case 5: // 手动结单
      return t('inStorage.billStatus5')
    default:
      return `状态${status}` // 未知状态显示原始值
  }
}
</script>

<template>
  <div class="in-storage-container">
    <!-- 页面标题 -->
    <div class="header">
      <el-button
        type="text"
        @click="goBack"
        class="back-btn"
        size="large"
      >
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1 class="title">{{ t('inStorage.title') }}</h1>
    </div>


    <!-- 主要内容区 -->
    <div class="content">
      <!-- 订单号输入区 -->
      <div class="input-section">
        <div class="input-row">
          <span class="input-label">{{ t('inStorage.orderNumber') }}</span>
          <el-input
            ref="orderInputRef"
            v-model="orderNumber"
            :placeholder="t('inStorage.orderNumberPlaceholder')"
            size="large"
            clearable
            @keyup.enter="handleConfirm"
            class="order-input"
            :disabled="loading"
          >
            <template #suffix>
              <svg t="1755229095243" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2446" width="16" height="16"><path d="M192 416C172.8 416 160 403.2 160 384L160 256c0-51.2 44.8-96 96-96l128 0c19.2 0 32 12.8 32 32S403.2 224 384 224L256 224C236.8 224 224 236.8 224 256l0 128C224 403.2 211.2 416 192 416z" fill="#272636" p-id="2447"></path><path d="M384 864 256 864c-51.2 0-96-44.8-96-96l0-128c0-19.2 12.8-32 32-32S224 620.8 224 640l0 128c0 19.2 12.8 32 32 32l128 0c19.2 0 32 12.8 32 32S403.2 864 384 864z" fill="#272636" p-id="2448"></path><path d="M768 864l-128 0c-19.2 0-32-12.8-32-32s12.8-32 32-32l128 0c19.2 0 32-12.8 32-32l0-128c0-19.2 12.8-32 32-32s32 12.8 32 32l0 128C864 819.2 819.2 864 768 864z" fill="#272636" p-id="2449"></path><path d="M832 416c-19.2 0-32-12.8-32-32L800 256c0-19.2-12.8-32-32-32l-128 0C620.8 224 608 211.2 608 192S620.8 160 640 160l128 0c51.2 0 96 44.8 96 96l0 128C864 403.2 851.2 416 832 416z" fill="#272636" p-id="2450"></path><path d="M832 544 192 544C172.8 544 160 531.2 160 512S172.8 480 192 480l640 0c19.2 0 32 12.8 32 32S851.2 544 832 544z" fill="#272636" p-id="2451"></path></svg>
            </template>
          </el-input>

          <el-button
            type="primary"
            size="large"
            @click="handleConfirm"
            :loading="loading"
            :disabled="!orderNumber.trim()"
            class="confirm-btn"
          >
            {{ t('common.confirm') }}
          </el-button>
        </div>

        <div class="action-buttons" v-if="showOrderDetails">
          <el-button @click="resetForm" size="small">{{ t('inStorage.reQuery') }}</el-button>
        </div>
      </div>

      <!-- 订单详情区域 -->
      <div class="details-section" v-if="showOrderDetails">
        <div class="details-header">
          <h3>{{ t('inStorage.orderDetailsTitle') }}</h3>
          <span class="order-info">{{ t('inStorage.orderInfo', { orderNumber }) }}</span>
        </div>

        <div class="details-list">
          <div
            v-for="item in orderDetails"
            :key="item.DetailId"
            class="detail-item"
            @click="handleItemClick(item)"
          >
            <!-- 主要信息行 -->
            <div class="item-main">
              <div class="item-info">
                <div class="product-name">{{ item.ProductName }}</div>
                <div class="product-code">{{ t('inStorage.productCode') }}: {{ item.ProductCode }}</div>
                <div class="product-spec">{{ item.ProductSpec }}</div>
              </div>
              <div class="item-quantity">
                <div class="quantity-row">
                  <span class="quantity-label">{{ t('inStorage.availableQty') }}:</span>
                  <span class="quantity available">{{ item.AvailableQty }}</span>
                </div>
                <div class="quantity-row">
                  <span class="quantity-label">{{ t('inStorage.qty') }}:</span>
                  <span class="quantity total">{{ item.Qty }}</span>
                </div>
              </div>
            </div>

            <!-- 详细信息行 -->
            <div class="item-details">
              <div class="detail-row">
                <span class="detail-label">{{ t('inStorage.linkBillNoERP') }}:</span>
                <span class="detail-value">{{ item.LinkBillNo }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">{{ t('inStorage.productBatch') }}:</span>
                <span class="detail-value">{{ item.ProductBatch }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">{{ t('inStorage.fQty') }}:</span>
                <span class="detail-value">{{ item.FQty }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">{{ t('inStorage.pQty') }}:</span>
                <span class="detail-value">{{ item.PQty }}</span>
              </div>
            </div>

            <!-- 底部信息行 -->
            <div class="item-footer">
              <div class="footer-left">
                <span class="warehouse">{{ t('inStorage.warehouseId') }}: {{ item.WarehouseId }}</span>
                <span class="sort-code">{{ t('inStorage.sortCode') }}: {{ item.SortCode }}</span>
              </div>
              <div class="footer-right">
                <span class="status" :class="getStatusClass(item.BillStatus)">
                  {{ getStatusText(item.BillStatus) }}
                </span>
              </div>
            </div>

            <div class="item-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.in-storage-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out;
}

.header {
  background: white;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  animation: slideInDown 0.6s ease-out;
}

.back-btn {
  position: absolute;
  left: 20px;
  color: #409eff;
  font-size: 20px;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: scale(1.1);
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  width: 100%;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  animation: slideInUp 0.6s ease-out;
}

.input-section {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.input-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.input-label {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  min-width: 60px;
}

.order-input {
  flex: 1;
}

.order-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.order-input :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

.order-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.confirm-btn {
  border-radius: 8px;
  font-weight: 600;
  min-width: 80px;
}

.action-buttons {
  text-align: center;
}

.details-section {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.details-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.order-info {
  font-size: 14px;
  opacity: 0.9;
}

.details-list {
  max-height: 400px;
  overflow-y: auto;
}

.detail-item {
  padding: 20px 25px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.detail-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.detail-item:last-child {
  border-bottom: none;
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.item-info {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.product-code {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 3px;
}

.product-spec {
  font-size: 13px;
  color: #95a5a6;
  line-height: 1.4;
  margin-top: 5px;
}

.item-quantity {
  text-align: right;
  min-width: 120px;
}

.quantity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.quantity-row:last-child {
  margin-bottom: 0;
}

.quantity-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-right: 8px;
}

.quantity {
  font-size: 16px;
  font-weight: 600;
}

.quantity.available {
  color: #27ae60;
}

.quantity.total {
  color: #3498db;
}

.item-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px 15px;
  margin: 10px 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.detail-label {
  color: #7f8c8d;
  font-weight: 500;
  margin-right: 8px;
}

.detail-value {
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  margin-top: 10px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.footer-right {
  display: flex;
  align-items: center;
}

.warehouse, .sort-code {
  color: #7f8c8d;
  font-size: 12px;
}

.status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

/* 订单状态样式 - 基于数值状态码 */
.status-unaudited {
  background: #fff3cd;
  color: #856404;
}

.status-audited {
  background: #d1ecf1;
  color: #0c5460;
}

.status-processing {
  background: #cce5ff;
  color: #0066cc;
}

.status-voided {
  background: #f8d7da;
  color: #721c24;
}

.status-auto-closed {
  background: #d4edda;
  color: #155724;
}

.status-manual-closed {
  background: #e2e3e5;
  color: #383d41;
}

.item-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #c0c4cc;
  transition: all 0.3s ease;
}

.detail-item:hover .item-arrow {
  color: #409eff;
  transform: translateY(-50%) translateX(5px);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content {
    padding: 15px;
  }

  .input-section {
    padding: 20px;
  }

  .input-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .input-label {
    min-width: auto;
  }

  .details-header {
    padding: 15px 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .detail-item {
    padding: 15px 20px;
  }

  .item-main {
    flex-direction: column;
    gap: 10px;
  }

  .item-quantity {
    text-align: left;
  }

  .item-details {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .detail-row {
    font-size: 12px;
  }

  .footer-left {
    gap: 2px;
  }

  .warehouse, .sort-code {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 15px;
  }

  .title {
    font-size: 20px;
  }

  .content {
    padding: 10px;
  }

  .input-section {
    padding: 15px;
  }

  .details-header {
    padding: 12px 15px;
  }

  .detail-item {
    padding: 12px 15px;
  }

  .item-details {
    padding: 10px 12px;
    margin: 8px 0;
  }

  .detail-row {
    font-size: 11px;
  }

  .product-spec {
    font-size: 12px;
  }

  .quantity-row {
    margin-bottom: 3px;
  }

  .quantity-label {
    font-size: 11px;
  }

  .quantity {
    font-size: 14px;
  }

  .item-arrow {
    right: 10px;
  }
}
</style>