import { request } from '@/utils/request'


// 定义API响应和数据类型
export interface ApiResponse<T> {
    data: T;
    message?: string;
    status?: string | number;
    code?: number;
}

// 用户信息类型
export interface UserInfo {
    userName?: string;
    token?: string;
    accessToken?: string;
    [key: string]: any;
}

// 登录参数类型
export interface LoginParams {
    userName: string;
    password: string;
    verificationCode?: string;
    UUID?: string;
}

// === 用户相关 API ===
export const userLogin = (data: LoginParams): Promise<ApiResponse<UserInfo>> => {
    return request.post('/api/user/login', data,);
};

export const getCurrentUserInfo = (): Promise<ApiResponse<UserInfo>> => {
    return request.post('/api/user/GetCurrentUserInfo');
};

export const getVerificationCode = (): Promise<ApiResponse<{img: string, uuid: string}>> => {
    return request.get('/api/User/getVierificationCode');
};

export const replaceToken = (): Promise<ApiResponse<any>> => {
    return request.post('/api/User/replaceToken');
};

// === 仓库管理 API ===
export const getAllPositions = (params?: any): Promise<ApiResponse<any>> => {
    return request.post('/api/Wms_Position/GetAllData', params || {});
};

// === 设备管理 API ===
export const getAllHardware = (params?: any): Promise<ApiResponse<any>> => {
    return request.post('/api/Wcs_Hardware/GetAllData', params || {});
};

export const writePlcData = (data: {StationNo: string, Key: string, Value: number}): Promise<ApiResponse<any>> => {
    return request.post('/api/Wcs_PlcDataItem/WritePlcData', data);
};

// === 历史记录 API ===
export const getTaskHistory = (params?: any): Promise<ApiResponse<any>> => {
    return request.post('/api/Wms_TaskHistory/GetAllData', params || {});
};

export const getCommandHistory = (params?: any): Promise<ApiResponse<any>> => {
    return request.post('/api/Wcs_Command/GetHistoryData', params || {});
};

export const getErrorHistory = (params?: any): Promise<ApiResponse<any>> => {
    return request.post('/api/Wcs_Error/GetHistoryData', params || {});
};




// === 示例 API (保留原有的示例代码) ===
export const postExample = (data: any): Promise<ApiResponse<any>>  => {
    return request.post('', data);
};

export const getExample = (id: string): Promise<ApiResponse<any>>  => {
    return request.get(`/xxx/${id}`);
}

export const getExample3 = (): Promise<ApiResponse<any>> => {
    return request.get( '/xxx');
}

export const getExample2 = (params: any): Promise<ApiResponse<any>>  => {
    if (!params) {
        return Promise.reject(new Error('params不能为空'));
    }
    return request.get('/xxx',  params );
};

export const deleteExample = (id: string): Promise<ApiResponse<any>>  => {
    return request.delete(`/xxx/${id}`)
}

export const putExample = (data: any): Promise<ApiResponse<any>>  => {
    return request.put('', data)
}


