# 环境配置说明

## 环境变量配置

项目支持多环境配置，通过不同的 `.env` 文件来管理不同环境的配置。

### 环境文件

- `.env` - 所有环境的默认配置
- `.env.development` - 开发环境配置
- `.env.debug` - 调试环境配置  
- `.env.production` - 生产环境配置

### 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_APP_TITLE` | 应用标题 | `STO-VUE` |
| `VITE_API_BASE_URL` | API 基础地址 | `http://***************:9100` |
| `VITE_DATA_VIEW_URL` | 数据大屏地址 | `http://localhost:9200/` |

### 运行命令

```bash
# 开发环境
npm run dev

# 调试环境
npm run dev:debug

# 构建生产环境
npm run build

# 构建调试环境
npm run build:debug

# 构建开发环境
npm run build:dev

# 预览生产环境
npm run preview

# 预览调试环境
npm run preview:debug
```

### HTTP 请求配置

项目已整合了完整的 HTTP 请求配置，包括：

1. **环境自适应**: 根据不同环境自动切换 API 地址
2. **Loading 管理**: 自动显示和隐藏加载状态
3. **错误处理**: 统一的错误处理和用户提示
4. **Token 管理**: 自动添加认证 token
5. **语言支持**: 自动添加语言头信息
6. **文件下载**: 支持文件下载功能
7. **数据大屏**: 支持数据大屏访问

### 使用示例

```typescript
import { userLogin, getAllPositions } from '@/api/index'
import http from '@/api/http'

// 使用封装的 API 方法
const loginResult = await userLogin({
  userName: 'admin',
  password: '123456'
})

// 直接使用 http 实例
const data = await http.post('/api/test', { id: 1 }, true)
```
