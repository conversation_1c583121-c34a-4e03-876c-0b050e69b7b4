# API 数据格式说明

## getOrderById 方法

### 请求参数
- 订单号 (字符串) 数组

### 返回数据格式

```javascript
[
  {
    "AvailableQty": 30,           // 可用数量
    "BillId": "BILL001",          // 订单内部编码
    "BillNo": "BN2024001",        // 内部订单号
    "BillStatus": 0,              // 订单状态 (数值: 0-5)
    "DetailId": "DT001",          // 订单内部明细编码
    "FQty": 0,                    // 已上架数量
    "LinkBillNoERP": "ERP2024001", // ERP订单号
    "PQty": 0,                    // 已组盘数量
    "ProductBatch": "BATCH001",   // 产品批次
    "ProductCode": "PC001",       // 产品编码
    "ProductName": "产品A",        // 产品名称
    "ProductSpec": "高品质产品A，规格齐全", // 产品描述
    "Qty": 100,                   // 订单明细总数
    "SortCode": "001",            // 订单明细序号
    "WarehouseId": "WH001"        // 仓库编码
  }
]
```

### 字段说明

| 字段名 | 类型 | 说明 | 国际化键名 |
|--------|------|------|-----------|
| AvailableQty | Number | 可用数量 | inStorage.availableQty |
| BillId | String | 订单内部编码 | inStorage.billId |
| BillNo | String | 内部订单号 | inStorage.billNo |
| BillStatus | String | 订单状态 | inStorage.billStatus |
| DetailId | String | 订单明细编码 | inStorage.detailId |
| FQty | Number | 已上架数量 | inStorage.fQty |
| LinkBillNoERP | String | ERP订单号 | inStorage.linkBillNoERP |
| PQty | Number | 已组盘数量 | inStorage.pQty |
| ProductBatch | String | 产品批次 | inStorage.productBatch |
| ProductCode | String | 产品编码 | inStorage.productCode |
| ProductName | String | 产品名称 | inStorage.productName |
| ProductSpec | String | 产品描述 | inStorage.productSpec |
| Qty | Number | 订单总数 | inStorage.qty |
| SortCode | String | 明细序号 | inStorage.sortCode |
| WarehouseId | String | 仓库编码 | inStorage.warehouseId |

### 订单状态值 (BillStatus)

| 数值 | 中文 | 英文 | 泰文 | CSS类名 |
|------|------|------|------|--------|
| 0 | 未审核 | Unaudited | ยังไม่ได้ตรวจสอบ | status-unaudited |
| 1 | 已审核 | Audited | ตรวจสอบแล้ว | status-audited |
| 2 | 操作中 | In Operation | กำลังดำเนินการ | status-processing |
| 3 | 已作废 | Voided | ยกเลิกแล้ว | status-voided |
| 4 | 自动结单 | Auto Closed | ปิดอัตโนมัติ | status-auto-closed |
| 5 | 手动结单 | Manual Closed | ปิดด้วยตนเอง | status-manual-closed |

## 页面显示布局

### 订单详情列表项结构

每个订单详情项包含以下信息区域：

1. **主要信息行**
   - 产品名称 (ProductName)
   - 产品编码 (ProductCode)
   - 产品描述 (ProductSpec)
   - 可用数量 (AvailableQty)
   - 订单总数 (Qty)

2. **详细信息区域**
   - ERP订单号 (LinkBillNoERP)
   - 产品批次 (ProductBatch)
   - 已上架数量 (FQty)
   - 已组盘数量 (PQty)

3. **底部信息行**
   - 仓库编码 (WarehouseId)
   - 明细序号 (SortCode)
   - 订单状态 (BillStatus)

### 点击跳转参数

点击订单详情项时，会跳转到详情页面并传递以下参数：

```javascript
{
  billId: item.BillId,
  detailId: item.DetailId,
  productName: item.ProductName,
  productCode: item.ProductCode,
  productSpec: item.ProductSpec,
  availableQty: item.AvailableQty,
  totalQty: item.Qty,
  productBatch: item.ProductBatch,
  billNo: item.BillNo,
  linkBillNoERP: item.LinkBillNoERP,
  warehouseId: item.WarehouseId
}
```

## 国际化支持

所有字段标签和状态文本都支持中文、英文、泰文三种语言的国际化显示。

### 使用方法

在组件中使用 `t()` 函数进行翻译：

```javascript
// 字段标签
t('inStorage.productName')  // 产品名称 / Product Name / ชื่อสินค้า

// 状态文本
getStatusText(item.BillStatus)  // 根据状态值返回对应语言的文本
```

## 移动端适配

- 响应式布局，适配不同屏幕尺寸
- 详细信息区域在移动端采用单列布局
- 字体大小和间距针对移动设备优化
- 触摸友好的交互设计
