<template>
  <div class="conveyor-container">
    <el-card shadow="never">
       <template #header>
        <div class="card-header">
          <span>输送线状态监控</span>
          <div class="control-group">
          <el-button type="primary"  :icon="RefreshRight" @click="refreshData">刷新</el-button>
          <el-select v-model="selectedStationId" placeholder="选择工位" style="width: 180px;">
            <el-option 
              v-for="station in stations" 
              :key="station.Id" 
              :label="station.Name" 
              :value="station.Id" 
            />
          </el-select>
          </div>
        </div>
      </template>

      <div v-if="selectedStation">
<!--        <div class="conveyor-visualization" v-if="plcDevice?.Enabled === 1">-->
<!--          <div class="conveyor-belt" :class="plcDevice?.Enabled === 1 ? 'running' : 'offline'">-->
<!--            <div class="pallet" :title="'工位: ' + selectedStation.Name">-->
<!--              <el-icon><Box /></el-icon>-->
<!--              <span>{{ selectedStation.No }}</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <el-descriptions :column="3" border>
          <el-descriptions-item label="PLC设备">{{ plcDevice && plcDevice.Name || '---' }}</el-descriptions-item>
          <el-descriptions-item label="工位名称">{{ selectedStation.Name }}</el-descriptions-item>
<!--          <el-descriptions-item label="工位编号">{{ selectedStation.No }}</el-descriptions-item>-->

          <el-descriptions-item label="IP地址">{{ plcDevice && plcDevice.Ip || '---' }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ plcDevice && plcDevice.Port || '---' }}</el-descriptions-item>
<!--          <el-descriptions-item label="PLC运行状态">-->
<!--            <el-tag :type="statusMap[plcDevice?.Enabled === 1 ? 'running' : 'offline'].type" size="small">-->
<!--              {{ statusMap[plcDevice?.Enabled === 1 ? 'running' : 'offline'].text }}-->
<!--            </el-tag>-->
<!--          </el-descriptions-item>-->
          <el-descriptions-item label="任务编号" 
            v-if="stationData.rTaskNo !== undefined && stationData.rTaskNo !== null">
            {{ stationData.rTaskNo || '---' }}
          </el-descriptions-item>
          <el-descriptions-item label="目标编号"
            v-if="stationData.rDestStationNo !== undefined && stationData.rDestStationNo !== null">
            {{ stationData.rDestStationNo|| '---'  }}
          </el-descriptions-item>
          <el-descriptions-item label="是否有货"
            v-if="stationData.hasMaterial !== undefined && stationData.hasMaterial !== null">
            <el-tag :type="stationData.hasMaterial ? 'success' : 'info'" size="small">
              {{ stationData.hasMaterial ? '有' : '无' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="运行状态"
            v-if="stationData.stationWorkStatus !== undefined && stationData.stationWorkStatus !== null">
            <el-tag :type="stationData.stationWorkStatus ? 'success' : 'info'" size="small">
              {{ stationData.stationWorkStatus ? '运行中' : '未运行' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="可放货"
            v-if="stationData.canPut !== undefined && stationData.canPut !== null">
            <el-tag :type="stationData.canPut ? 'success' : 'info'" size="small">
              {{ stationData.canPut ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="可取货"
            v-if="stationData.canGet !== undefined && stationData.canGet !== null">
            <el-tag :type="stationData.canGet ? 'success' : 'info'" size="small">
              {{ stationData.canGet ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="是否故障"
            v-if="stationData.stationFault !== undefined && stationData.stationFault !== null">
            <el-tag :type="stationData.stationFault ? 'danger' : 'success'" size="small">
              {{ stationData.stationFault ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="入库请求"
            v-if="stationData.inAsk !== undefined && stationData.inAsk !== null">
            <el-tag :type="stationData.inAsk ? 'warning' : 'info'" size="small">
              {{ stationData.inAsk ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="外形检测是否合格"
            v-if="stationData.appearanceInspectionResult !== undefined && stationData.appearanceInspectionResult !== null">
            <el-tag :type="stationData.appearanceInspectionResult === 1 ? 'success' : stationData.appearanceInspectionResult === 2 ? 'danger' : 'info'" size="small">
              {{ appearanceInspectionResultMap[stationData.appearanceInspectionResult] }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="外形检测高度类型"
            v-if="stationData.heightType !== undefined && stationData.heightType !== null">
            <el-tag :type="stationData.heightType === 0 ? 'info' : 'success'" size="small">
            {{ heightTypeMap[stationData.heightType] }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="高度超限"
            v-if="stationData.heightLimit !== undefined && stationData.heightLimit !== null">
            <el-tag :type="stationData.heightLimit ? 'danger' : 'success'" size="small">
              {{ stationData.heightLimit ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="前超限"
            v-if="stationData.frontLimit !== undefined && stationData.frontLimit !== null">
            <el-tag :type="stationData.frontLimit ? 'danger' : 'success'" size="small">
              {{ stationData.frontLimit ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="后超限"
            v-if="stationData.backendLimit !== undefined && stationData.backendLimit !== null">
            <el-tag :type="stationData.backendLimit ? 'danger' : 'success'" size="small">
              {{ stationData.backendLimit ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="左超限"
            v-if="stationData.leftLimit !== undefined && stationData.leftLimit !== null">
            <el-tag :type="stationData.leftLimit ? 'danger' : 'success'" size="small">
              {{ stationData.leftLimit ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="右超限"
            v-if="stationData.rightLimit !== undefined && stationData.rightLimit !== null">
            <el-tag :type="stationData.rightLimit === true ? 'danger' : 'success'" size="small">
              {{ stationData.rightLimit === true ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

<!--        <el-divider>消息</el-divider>-->
<!--        <div class="message-panel">-->
<!--          <el-scrollbar height="200px" ref="messageScrollbar">-->
<!--            <div v-for="(msg, index) in messages" -->
<!--                 :key="index" -->
<!--                 class="message-item" -->
<!--                 :class="{-->
<!--                   'new-message': msg.isNew,-->
<!--                   'isAnimating': msg.isAnimating-->
<!--                 }">-->
<!--              <div class="message-time">{{ msg.time }}</div>-->
<!--              <div class="message-content">-->
<!--                <el-tag size="small" :type="msg.type">{{ msg.title }}</el-tag>-->
<!--                <span>{{ msg.content }}</span>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div v-if="messages.length === 0" class="empty-message">-->
<!--              暂无消息-->
<!--            </div>-->
<!--          </el-scrollbar>-->
<!--        </div>-->

        <el-divider>设备控制</el-divider>
        <div class="control-buttons">
          <el-popconfirm title="确定要发送[放行]命令吗?" @confirm="sendCommand('ScanCheckPass')">
            <template #reference>
              <el-button type="success" :icon="VideoPlay">放行</el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm title="确定要发送[回退]命令吗?" @confirm="sendCommand('ScanCheckFail')">
            <template #reference>
              <el-button type="danger" :icon="VideoPause">回退</el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm title="确定要发送[故障复位]命令吗?" @confirm="sendCommand('WcsRecover')">
            <template #reference>
              <el-button type="warning" :icon="Refresh">故障复位</el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm title="确定要发送[取货]命令吗?" @confirm="sendAgvCommand('GetFinish')">
            <template #reference>
              <el-button type="primary" >取货完成</el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm title="确定要发送[放货]命令吗?" @confirm="sendAgvCommand('PutFinish')">
            <template #reference>
              <el-button type="primary">放货完成</el-button>
            </template>
          </el-popconfirm>
          <el-button type="info" :icon="Edit" @click="openEditDialog">任务编辑</el-button>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog
          v-model="editDialogVisible"
          title="任务编辑"
          width="500px"
          :close-on-click-modal="false"
        >
          <el-form :model="editForm" ref="editFormRef" label-width="120px" :rules="editFormRules">
            <el-form-item label="工位编号" prop="StationNo">
              <el-input v-model="editForm.StationNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="任务编号" prop="WTaskNo">
              <el-input v-model="editForm.WTaskNo" :placeholder="getDataTypeLabel('WTaskNo')"></el-input>
            </el-form-item>
            <el-form-item label="目标编号" prop="WDestStationNo">
              <el-input v-model="editForm.WDestStationNo" :placeholder="getDataTypeLabel('WDestStationNo')"></el-input>
            </el-form-item>

          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="editDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitEditForm">确定</el-button>
            </span>
          </template>
        </el-dialog>

      </div>
       <el-empty v-else description="请先选择一个工位" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick, reactive } from 'vue';
import { ElMessage, ElNotification } from 'element-plus'
import { VideoPlay, VideoPause, Refresh, Box, RefreshRight, Edit } from '@element-plus/icons-vue';
import { useRoute } from 'vue-router';
import { useDeviceStore } from '../../store/device';
import http from '@/api/http'
import * as signalR from '@microsoft/signalr';

const route = useRoute();
const deviceStore = useDeviceStore();

const selectedStationId = ref(null);
const stationData = ref({
  rTaskNo: '',
  rDestStationNo: '',
  hasMaterial: '',
  stationWorkStatus: '',
  canPut: '',
  canGet: '',
  stationFault: '',
  inAsk: '',
  appearanceInspectionResult: '',
  heightType: '',
  heightLimit: '',
  frontLimit: '',
  backendLimit: '',
  leftLimit: '',
  rightLimit: ''
});

// 消息列表
const messages = ref([]);
let connection = null;
const messageScrollbar = ref(null);

// 初始化SignalR连接
const initSignalR = async () => {
  try {
    // 获取当前登录用户信息
    const result = await http.post('api/user/GetCurrentUserInfo');
    if (result && result.data) {
      // 创建连接
      connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect()
        .withUrl(`${http.ipAddress}wcsHub`)
        .build();

      // 启动连接
      await connection.start();
      console.log('SignalR连接成功');

      // 自动重连成功后的处理
      connection.onreconnected((connectionId) => {
        console.log('SignalR重新连接成功:', connectionId);
        addMessage('系统', '连接已恢复', 'success');
      });

      // 接收消息回调
      connection.on('StationMsg', (message) => {
        addMessage('工位消息', message, 'info');
      });
      connection.on('StrackerMsg', (message) => {
        console.log('收到堆垛机消息:', message);
        addMessage('堆垛机消息', message, 'info');
      });
    }
  } catch (err) {
    console.error('SignalR连接失败:', err);
    addMessage('系统', '连接失败', 'danger');
  }
};

// 添加消息到列表
const addMessage = (title, content, type = 'info') => {
  const now = new Date();
  const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  
  // 先添加一个占位消息，用于动画效果
  const newMessage = {
    time: timeStr,
    title: title,
    content: content,
    type: type,
    isNew: true,
    isAnimating: true
  };
  
  messages.value.unshift(newMessage);

  // 最多保留50条消息
  if (messages.value.length > 50) {
    messages.value.pop();
  }
  
  // 滚动到顶部并在短暂延迟后移除新消息高亮
  nextTick(() => {
    if (messageScrollbar.value) {
      messageScrollbar.value.setScrollTop(0);
    }
    
    // 300ms后结束入场动画
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isAnimating = false;
      }
    }, 300);
    
    // 2秒后移除新消息高亮
    setTimeout(() => {
      if (messages.value.length > 0) {
        messages.value[0].isNew = false;
      }
    }, 2000);
  });
};

// 获取当前选中的PLC设备
const plcDevice = computed(() => deviceStore.getSelectedPlc);

// 获取当前PLC设备下的所有工位
const stations = computed(() => deviceStore.getStationsOfSelectedPlc);

// 获取当前选中的工位
const selectedStation = computed(() => {
  if (!selectedStationId.value) return null;
  return stations.value.find(station => station.Id === selectedStationId.value);
});

const fetchStationData = async () => {
  if (!selectedStation.value || !selectedStation.value.No) return;
  
  try {
    const res = await http.post(`/api/Wcs_PlcDataItem/ReadStationData?StationNo=${selectedStation.value.No}`, {});
    if (res && res.data) {
      stationData.value = res.data;
    }
  } catch (err) {
    console.error('获取工位数据失败:', err);
    ElMessage.error('获取工位数据失败');
  }
};

// 监听选中工位变化，获取工位数据
watch(() => selectedStation.value, (newStation) => {
  if (newStation) {
    fetchStationData();
    // addMessage('系统', `已选择工位: ${newStation.Name}`, 'success');
  }
}, { immediate: true });

// 数据类型映射
const dataTypeMap = {
  0: '未知',
  1: '短整型',
  2: '整型',
  3: '布尔值',
  4: '字符串'
};

const heightTypeMap = {
  0: '无',
  1: '一类高度',
  2: '二类高度',
  3: '三类高度',

};
const appearanceInspectionResultMap = {
  0: '无',
  1: '合格',
  2: '不合格',
};

// 监听路由变化
watch(() => route.query.deviceId, (newDeviceId) => {
  if (newDeviceId && parseInt(newDeviceId)) {
    deviceStore.setSelectedPlc(parseInt(newDeviceId));
  } else {
    // 如果没有设备ID，尝试找到第一个输送线设备
    const conveyorDevices = deviceStore.getPlcDevices.filter(device => device.Name.includes('输送线'));

    if (conveyorDevices.length > 0) {
      deviceStore.setSelectedPlc(conveyorDevices[0].Id);
    }
  }
  
  // 如果有工位，默认选择第一个
  if (stations.value.length > 0) {
    selectedStationId.value = stations.value[0].Id;
  }
  console.log('plcDevice', plcDevice);
}, { immediate: true });

// 监听工位列表变化
watch(() => stations.value, (newStations) => {
  if (newStations.length > 0 && !selectedStationId.value) {
    selectedStationId.value = newStations[0].Id;
  }
}, { immediate: true });

const statusMap = ref({
  running: { text: '运行中', type: 'success' },
  idle: { text: '空闲', type: 'primary' },
  error: { text: '故障', type: 'danger' },
  offline: { text: '离线', type: 'info' },
});
const refreshData = () => {

  fetchStationData();
};

const sendCommand = async (key) => {
  if (!selectedStation.value || !selectedStation.value.No) {
    ElMessage.error('缺少工位号数据');
    return;
  }
  
  const url = '/api/Wcs_PlcDataItem/WritePlcData';
  let data= {
    StationNo:selectedStation.value.No,
    Key:key,
    Value:1
  };
  try {
    const res = await http.post(url, data);
    if (res && res.status) {
      ElNotification({
        title: '命令已发送',
        message: `已向 ${selectedStation.value.Name} 发送 [${getCommandName(key)}] 命令。`,
        type: 'success',
      });
    } else {
      ElNotification({
        title: '命令发送失败',
        message: res.message || 'plc连接不上',
        type: 'error',
      });
    }
  } catch (err) {
    console.error('发送命令失败:', err);
    ElNotification({
      title: '命令发送失败',
      message: '请检查网络连接或服务器状态',
      type: 'error',
    });
  }
};

const getCommandName = (key) => {
  switch(key) {
    case 'ScanCheckPass': return '放行';
    case 'ScanCheckFail': return '回退';
    case 'WcsRecover': return '故障复位';
    default: return key;
  }
};

// 发送AGV命令
const sendAgvCommand = async (key) => {
  if (!selectedStation.value || !selectedStation.value.No) {
    ElMessage.error('缺少工位号数据');
    return;
  }
  
  const url = `/api/Wcs_PlcDataItem/WritePlcData?StationNo=${selectedStation.value.No}&Key=${key}&value=true`;
  
  try {
    const res = await http.post(url, {});
    if (res && res.status) {
      ElNotification({
        title: '命令已发送',
        message: `已向 ${selectedStation.value.Name} 发送 [${key === 'PutFinish' ? 'AGV放货完成' : 'AGV取货完成'}] 命令。`,
        type: 'success',
      });
    } else {
      ElNotification({
        title: '命令发送失败',
        message: res.message || 'plc连接不上',
        type: 'error',
      });
    }
  } catch (err) {
    console.error('发送命令失败:', err);
    ElNotification({
      title: '命令发送失败',
      message: '请检查网络连接或服务器状态',
      type: 'error',
    });
  }
};

// 组件挂载时初始化SignalR连接
onMounted(() => {
  // initSignalR();
  //
  // // 定时刷新工位数据
  // timer = setInterval(() => {
  //   if (selectedStation.value) {
  //     fetchStationData();
  //   }
  // }, 10000); // 每10秒刷新一次
});

// 定时器变量
// let timer = null;

// 组件销毁时清除定时器
onUnmounted(() => {
  // if (timer) {
  //   clearInterval(timer);
  // }
  
  // 关闭SignalR连接
  // if (connection) {
  //   connection.stop();
  // }
});

// 编辑对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const editForm = reactive({
  StationNo: '',
  WTaskNo: '',
  WDestStationNo: '',
});

// 编辑表单验证规则
const editFormRules = reactive({
  WTaskNo: [
    { required: true, message: '请输入任务编号', trigger: 'blur' }
  ],
  WDestStationNo: [
    { required: true, message: '请输入目标编号', trigger: 'blur' }
  ]
});

// 打开编辑对话框
const openEditDialog = () => {
  if (!selectedStation.value || !selectedStation.value.No) {
    ElMessage.error('缺少工位号数据');
    return;
  }
  
  // 初始化表单数据
  editForm.StationNo = selectedStation.value.No;
  editForm.WTaskNo = '';
  editForm.WDestStationNo = '';
  
  editDialogVisible.value = true;
};

// 根据Code获取数据类型提示
const getDataTypeLabel = (code) => {
  if (!selectedStation.value || !selectedStation.value.Templates) return '请输入数据';
  
  const template = selectedStation.value.Templates.find(t => t.Code === code);
  if (!template) return '请输入数据';
  
  const dataType = template.DataType;
  let label = ref('');
  if (dataType === 1) {
    label.value = '数字';
  } else if (dataType === 2) {
    label.value = '数字';
  } else if (dataType === 3) {
    label.value = '布尔值';
  } else if (dataType === 4) {
    label.value = '字符串';
  }
  // return `请输入${dataTypeMap[dataType] || '数据'}`;
  return `请输入${label.value|| '数据'}`;
};

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return;
  
  editFormRef.value.validate(async (valid) => {
    if (valid) {
      const stationNet = {
        StationNo: editForm.StationNo,
        WTaskNo: editForm.WTaskNo,
        WDestStationNo: editForm.WDestStationNo
      };
      
      try {
        const res = await http.post('/api/Wcs_PlcDataItem/StationTask', stationNet);
        if (res && res.status) {
          ElNotification({
            title: '提交成功',
            message: '工位数据已更新',
            type: 'success'
          });
          editDialogVisible.value = false;
          // 刷新数据
          fetchStationData();
        } else {
          ElNotification({
            title: '提交失败',
            message: res.message || 'plc连接不上',
            type: 'error'
          });
        }
      } catch (err) {
        console.error('提交工位数据失败:', err);
        ElNotification({
          title: '提交失败',
          message: '请检查网络连接或服务器状态',
          type: 'error'
        });
      }
    } else {
      return false;
    }
  });
};

</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.control-group {
  display: flex;
  align-items: center;
}
.control-buttons {
  display: flex;
  gap: 10px;
}
.conveyor-visualization {
  margin: 20px 0;
}
.conveyor-belt {
  height: 80px;
  background-color: #dcdfe6;
  border-radius: 4px;
  border: 2px dashed #c0c4cc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
}
.conveyor-belt.running .pallet {
  animation: move-pallet 5s linear infinite;
}
.conveyor-belt.error {
  border-color: #f56c6c;
}
.pallet {
  width: 80px;
  height: 60px;
  background-color: #f2e2b2;
  border: 1px solid #e6a23c;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #b37b00;
  font-size: 20px;
}
.pallet span {
  font-size: 12px;
  margin-top: 4px;
}
@keyframes move-pallet {
  0% { transform: translateX(-150px); }
  100% { transform: translateX(150px); }
}
.message-panel {
  margin-top: 20px;
}
.message-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 8px 5px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.5s ease;
  opacity: 1;
  transform: translateY(0);
}
.message-item.new-message {
  background-color: rgba(64, 158, 255, 0.1);
  animation: highlight-new-message 2s ease;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.message-item.isAnimating {
  animation: slide-down 0.3s ease-out;
}
@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes highlight-new-message {
  0% {
    background-color: rgba(64, 158, 255, 0.3);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.2);
    box-shadow: 0 2px 10px rgba(64, 158, 255, 0.2);
  }
  100% {
    background-color: rgba(64, 158, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
}
.message-time {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}
.message-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}
.empty-message {
  text-align: center;
  color: #909399;
  padding: 20px;
}
.agv-control-buttons {
  display: flex;
  gap: 10px;
}
</style>