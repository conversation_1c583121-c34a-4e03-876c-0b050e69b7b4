<script setup>
import {ref, computed, onMounted, nextTick, watch} from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {ArrowLeft, Box, Location, Position} from "@element-plus/icons-vue";
import {StockInByBill} from "@/api/index.js";

const router = useRouter()
const { t } = useI18n()

const locationInput = ref('')
const quantityInput = ref('')
const locationInputRef = ref(null)
const quantityInputRef = ref(null)

const {productName, productCode, productSpec, availableQty,  productBatch, billNo, warehouseId,weight,SortCode} = defineProps({
  billId: String,
  detailId: String,
  productName: String,
  productCode: String,
  productSpec: String,
  SortCode: [String, Number],
  availableQty: [String, Number],
  totalQty: [String, Number],
  productBatch: String,
  billNo: String,
  linkBillNoERP: String,
  warehouseId: String,
  weight: [String, Number],
})


// 页面加载完成后自动聚焦到第一个输入框
onMounted(async () => {
  await nextTick()
  if (locationInputRef.value) {
    locationInputRef.value.focus()
  }
})
watch(
    () => availableQty,
    (val) => {
      if (val != null) {
        quantityInput.value = val
      }
    },
    { immediate: true }
)


const handleQuantityConfirm = async () => {
  if (!locationInput.value.trim()) {
    ElMessage.warning(t('inStorageDetails.locationRequired'))
    return
  }
  // if (!quantityInput.value.trim()) {
  //   ElMessage.warning(t('inStorageDetails.quantityRequired'))
  //   return
  // }

  const inputQuantity = parseInt(quantityInput.value)
  const maxQuantity = availableQty

  if (inputQuantity > maxQuantity) {
    ElMessage.error(t('inStorageDetails.quantityExceedsMax', { max: maxQuantity }))
    return
  }

  if (inputQuantity <= 0) {
    ElMessage.error(t('inStorageDetails.quantityMustBePositive'))
    return
  }

  try {
    const data = {
      stockInBillList: [
        {
          billNo: billNo,
          sortCode: SortCode,
          qty: inputQuantity,
          weight: weight,
          remark:""
       }
      ],
      palletCode: locationInput.value,
      warehouseId: warehouseId,
      laneWay: 0,
      weight: weight,
      dataSource: 0
    }
    const response = await StockInByBill(data)
    if (response.Status) {
      ElMessage.success(t('inStorageDetails.inStorageSuccess', {quantity: inputQuantity}))
      router.back()
    } else {
      ElMessage.error(t('inStorageDetails.inStorageError'))
    }


  } catch (e) {
    ElMessage.error(e.message)
    console.error(e.message)
  }

}

const goBack = () => {
  router.back()
}

const resetInputs = () => {
  locationInput.value = ''
  quantityInput.value = ''
  nextTick(() => {
    if (locationInputRef.value) {
      locationInputRef.value.focus()
    }
  })
}
</script>

<template>
  <div class="details-container">
    <!-- 页面标题 -->
    <div class="header">
      <el-button
        type="text"
        @click="goBack"
        class="back-btn"
        size="large"
      >
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1 class="title">{{ t('inStorageDetails.title') }}</h1>
    </div>

    <!-- 产品信息 -->
    <div class="product-info">
      <div class="info-card">
        <div class="card-header">
          <h3 class="product-title">{{ productName }}</h3>
          <div class="product-subtitle">{{ t('inStorageDetails.productInfo') }}</div>
        </div>

        <div class="info-grid">
          <div class="info-row">
            <span class="label">{{ t('inStorageDetails.productName') }}:</span>
            <span class="value">{{ productName }}</span>
          </div>
          <div class="info-row">
            <span class="label">{{ t('inStorageDetails.productCode') }}:</span>
            <span class="value">{{ productCode }}</span>
          </div>
          <div class="info-row">
            <span class="label">{{ t('inStorageDetails.productBatch') }}:</span>
            <span class="value">{{ productBatch }}</span>
          </div>
          <div class="info-row">
            <span class="label">{{ t('inStorageDetails.availableQty') }}:</span>
            <span class="value available-qty">{{ availableQty }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作区域 -->
    <div class="operation-section">
      <!-- 库位输入 -->
      <div class="input-group">
        <div class="input-header">
          <el-icon><Location /></el-icon>
          <span>{{ t('inStorageDetails.locationScan') }}</span>
        </div>
        <div class="input-row">
          <el-input
            ref="locationInputRef"
            v-model="locationInput"
            :placeholder="t('inStorageDetails.locationPlaceholder')"
            size="large"
            clearable

            class="main-input"
          >
            <template #prefix>
              <el-icon><Position /></el-icon>
            </template>
            <template #suffix>
              <svg t="1755229095243" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2446" width="16" height="16"><path d="M192 416C172.8 416 160 403.2 160 384L160 256c0-51.2 44.8-96 96-96l128 0c19.2 0 32 12.8 32 32S403.2 224 384 224L256 224C236.8 224 224 236.8 224 256l0 128C224 403.2 211.2 416 192 416z" fill="#272636" p-id="2447"></path><path d="M384 864 256 864c-51.2 0-96-44.8-96-96l0-128c0-19.2 12.8-32 32-32S224 620.8 224 640l0 128c0 19.2 12.8 32 32 32l128 0c19.2 0 32 12.8 32 32S403.2 864 384 864z" fill="#272636" p-id="2448"></path><path d="M768 864l-128 0c-19.2 0-32-12.8-32-32s12.8-32 32-32l128 0c19.2 0 32-12.8 32-32l0-128c0-19.2 12.8-32 32-32s32 12.8 32 32l0 128C864 819.2 819.2 864 768 864z" fill="#272636" p-id="2449"></path><path d="M832 416c-19.2 0-32-12.8-32-32L800 256c0-19.2-12.8-32-32-32l-128 0C620.8 224 608 211.2 608 192S620.8 160 640 160l128 0c51.2 0 96 44.8 96 96l0 128C864 403.2 851.2 416 832 416z" fill="#272636" p-id="2450"></path><path d="M832 544 192 544C172.8 544 160 531.2 160 512S172.8 480 192 480l640 0c19.2 0 32 12.8 32 32S851.2 544 832 544z" fill="#272636" p-id="2451"></path></svg>
            </template>
          </el-input>

<!--          <el-button-->
<!--            type="primary"-->
<!--            size="large"-->
<!--            @click="handleLocationConfirm"-->
<!--            :disabled="!locationInput.trim()"-->
<!--            class="confirm-btn"-->
<!--          >-->
<!--            {{ t('common.confirm') }}-->
<!--          </el-button>-->
        </div>
        <div class="input-header">
          <el-icon><Box /></el-icon>
          <span>{{ t('inStorageDetails.inStorageQuantity') }}</span>
        </div>
        <div class="input-row">
          <el-input-number
              ref="quantityInputRef"
              v-model="quantityInput"
              :min="0"
              :max="availableQty"
              size="large"
              controls-position="right"
              class="quantity-input"
              :placeholder="t('inStorageDetails.quantityPlaceholder')"
          />

          <el-button
              type="success"
              size="large"
              @click="handleQuantityConfirm"
              :disabled="!quantityInput || quantityInput <= 0"
              class="submit-btn"
          >
            {{ t('common.submit') }}
          </el-button>
        </div>
      </div>

      <!-- 重置按钮 -->
      <div class="action-buttons">
        <el-button @click="resetInputs" size="large" class="reset-btn">
          {{ t('common.reset') }}
        </el-button>
      </div>
    </div>


  </div>
</template>

<style scoped>
.details-container {
  width: 100%;
  height: 100vh;

  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out;
}

.header {
  background: white;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  animation: slideInDown 0.6s ease-out;
}

.back-btn {
  position: absolute;
  left: 20px;
  color: #409eff;
  font-size: 20px;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: scale(1.1);
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  width: 100%;
}

.product-info {
  padding: 20px;
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.info-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409eff;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.card-header {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.product-title {
  margin: 0 0 5px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.product-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
  opacity: 0.8;
}

.info-grid {
  display: grid;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.info-row:hover {
  background: #e9ecef;
  border-color: #409eff;
  transform: translateY(-1px);
}

.label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  min-width: 80px;
}

.value {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 12px;
}

.available-qty {
  color: #28a745;
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.operation-section {
  flex: 1;
  padding: 0 20px 20px;
  overflow-y: auto;
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.input-group {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.input-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.main-input {
  flex: 1;
}

.main-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.main-input :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

.main-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.quantity-input {
  flex: 1;
}

.quantity-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.quantity-input :deep(.el-input__wrapper:hover) {
  border-color: #67c23a;
}

.quantity-input :deep(.el-input__wrapper.is-focus) {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.unit-text {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
  white-space: nowrap;
}

.confirm-btn {
  border-radius: 8px;
  font-weight: 600;
  min-width: 80px;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.submit-btn {
  border-radius: 8px;
  font-weight: 600;
  min-width: 80px;
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.action-buttons {
  text-align: center;
}

.reset-btn {
  border-radius: 8px;
  font-weight: 600;
  min-width: 100px;
}

.data-preview {
  background: white;
  border-radius: 16px 16px 0 0;
  margin: 0 20px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out 0.3s both;
}

.preview-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 25px;
  border-radius: 16px 16px 0 0;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-content {
  padding: 40px 25px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  text-align: center;
  color: #c0c4cc;
}

.empty-state p {
  margin: 15px 0 0 0;
  font-size: 16px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .product-info {
    padding: 15px;
  }

  .info-card {
    padding: 20px;
  }

  .product-title {
    font-size: 18px;
  }

  .info-row {
    padding: 10px 12px;
  }

  .label {
    font-size: 13px;
    min-width: 70px;
  }

  .value {
    font-size: 15px;
  }

  .available-qty {
    font-size: 16px;
  }

  .operation-section {
    padding: 0 15px 15px;
  }

  .input-group {
    padding: 20px;
  }

  .input-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .unit-text {
    text-align: center;
  }

  .data-preview {
    margin: 0 15px;
  }

  .preview-header {
    padding: 12px 20px;
  }

  .preview-content {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 15px;
  }

  .title {
    font-size: 20px;
  }

  .product-info {
    padding: 10px;
  }

  .info-card {
    padding: 15px;
  }

  .product-title {
    font-size: 16px;
  }

  .product-subtitle {
    font-size: 12px;
  }

  .info-row {
    padding: 8px 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .label {
    font-size: 12px;
    min-width: auto;
  }

  .value {
    font-size: 14px;
    text-align: left;
    margin-left: 0;
  }

  .available-qty {
    font-size: 15px;
  }

  .operation-section {
    padding: 0 10px 10px;
  }

  .input-group {
    padding: 15px;
  }

  .info-card {
    padding: 20px;
  }

  .data-preview {
    margin: 0 10px;
  }
}
</style>