export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    back: 'Back',
    reset: 'Reset',
    submit: 'Submit',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info'
  },
  
  // Index page
  index: {
    title: 'Kitech',
    sectionTitle: 'Get Data',
    scanOrder: 'Scan Order',
    inStorage: 'In Storage'
  },
  
  // Order scan page
  orderScan: {
    title: 'Scan Order',
    orderNumber: 'OrderId',
    orderNumberPlaceholder: 'Please enter or scan orderId',
    orderNumberRequired: 'Please enter orderId',
    orderConfirmSuccess: 'OrderId: {orderId} confirmed successfully',
    orderConfirmFailed: 'Failed to confirm orderId: {orderId}'
  },
  
  // In storage page
  inStorage: {
    title: 'In Storage Management',
    orderNumber: 'OrderId',
    orderNumberPlaceholder: 'Please enter orderId',
    orderNumberRequired: 'Please enter orderId',
    orderDetailsTitle: 'Order Details',
    orderInfo: 'Order No: {orderId}',
    reQuery: 'Re-query',
    getOrderDetailsSuccess: 'Order details retrieved successfully',
    getOrderDetailsFailed: 'Failed to retrieve order details',
    // Order detail fields
    availableQty: 'Available Qty',
    billId: 'Bill ID',
    billNo: 'Bill No',
    billStatus: 'Bill Status',
    detailId: 'Detail ID',
    fQty: 'Finished Qty',
    linkBillNoERP: 'ERP Order No',
    pQty: 'Packed Qty',
    productBatch: 'Product Batch',
    productCode: 'Product Code',
    productName: 'Product Name',
    productSpec: 'Product Spec',
    qty: 'Total Qty',
    sortCode: 'Sort Code',
    warehouseId: 'Warehouse ID',
    // Bill Status (BillStatus numeric values)
    billStatus0: 'Unaudited',
    billStatus1: 'Audited',
    billStatus2: 'In Operation',
    billStatus3: 'Voided',
    billStatus4: 'Auto Closed',
    billStatus5: 'Manual Closed',
    routerMessage:'Not editable, Available quantity is 0'
  },
  
  // In storage details page
  inStorageDetails: {
    title: 'In Storage Details',
    productInfo: 'Product Information',
    productName: 'Product Name',
    productCode: 'Product Code',
    productBatch: 'Product Batch',
    availableQty: 'Available Qty',
    plannedQuantity: 'Planned Quantity',
    suggestedLocation: 'Suggested Pallet',
    locationScan: 'Pallet',
    locationPlaceholder: 'Please scan or enter Pallet Code',
    locationRequired: 'Please enter Pallet Code',
    locationConfirmSuccess: 'Pallet confirmed successfully',
    inStorageQuantity: ' Quantity',
    quantityPlaceholder: 'Please enter quantity',
    quantityRequired: 'Please enter quantity',
    quantityExceedsMax: 'Input quantity cannot exceed {max}',
    quantityMustBePositive: 'Input quantity must be greater than 0',
    inStorageSuccess: 'operation successful, quantity: {quantity}',
    inStorageError: 'operation failed',
    operationRecord: 'Operation Record',
    noOperationRecord: 'No operation record'
  },
  
  // Language switch
  language: {
    chinese: '中文',
    english: 'English',
    thai: 'ไทย'
  }
}
