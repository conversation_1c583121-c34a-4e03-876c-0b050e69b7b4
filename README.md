# PDA 移动端仓库管理系统

这是一个基于 Vue 3 + Element Plus 开发的移动端PDA仓库管理系统，专为移动设备和PDA设备优化。

## 功能特性

### 🏠 主页面 (Index.vue)
- 响应式布局设计，适配各种移动设备
- 美观的渐变背景和卡片式布局
- 流畅的动画效果和交互反馈
- 功能模块：
  - 扫描订单
  - 入库管理

![image-20250815155056409](imgs/img_6.png)

![image-20250815155056409](imgs/img_5.png)

![image-20250815155056409](imgs/img_4.png)

### 📱 扫描订单 (OrderScan/Index.vue)
- 自动聚焦到订单号输入框
- 支持键盘回车确认
- 实时输入验证
- 移动端优化的输入体验

![image-20250815155056409](imgs/img_3.png)

### 📦 入库管理 (InStorage/Index.vue)
- 订单号查询功能
- 动态显示订单详情列表（支持完整的API数据格式）
- 详细的订单信息展示：
  - 产品基本信息（名称、编码、描述）
  - 数量信息（可用数量、总数量、已上架、已组盘）
  - 订单信息（ERP订单号、批次、仓库、状态）
- 点击详情项跳转到详情页面
- 支持重新查询功能
- 完整的国际化支持

![image-20250815155056409](imgs/img_2.png)

![image-20250815155056409](imgs/img_1.png)

### 📋 入库详情 (InStorage/Details.vue)
- **产品信息卡片**:
  - 产品名称、编码、批次信息
  - 可用数量突出显示（绿色渐变效果）
  - 现代化卡片设计，支持悬停交互
- 库位扫描输入
- 数量输入控制（带最大值限制）
- 操作记录预览区域
- 表单验证和错误提示
- 完整的国际化支持

![image-20250815155056409](imgs/img.png)

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vue Router 4** - 官方路由管理器
- **Element Plus** - Vue 3 UI组件库
- **Vue I18n** - 国际化解决方案
- **Vite** - 现代化构建工具
- **CSS3** - 响应式设计和动画效果

## 🌍 国际化功能

### 支持语言
- **中文 (zh-CN)** - 简体中文
- **英文 (en-US)** - 美式英语
- **泰文 (th-TH)** - 泰语

### 语言切换
- 屏幕右下角固定悬浮按钮
- 点击显示语言选择菜单
- 支持实时切换，无需刷新页面
- 语言设置自动保存到本地存储
- 页面跳转后保持选择的语言

### 国际化特性
- 全页面文本国际化
- 动态消息提示国际化
- 表单验证消息国际化
- 支持参数化翻译（如订单号、数量等）
- 优雅的语言切换动画效果

## 移动端优化

### 响应式设计
- 支持多种屏幕尺寸 (320px - 768px+)
- 触摸友好的按钮尺寸 (最小44px)
- 自适应字体大小和间距

### 用户体验
- 流畅的页面切换动画
- 触摸反馈效果
- 自动聚焦输入框
- 防误触设计

### 性能优化
- 路由懒加载
- CSS动画硬件加速
- 滚动条优化
- 安全区域适配

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
│   └── LanguageSwitcher.vue # 语言切换组件
├── locales/         # 国际化文件
│   ├── index.js            # i18n配置
│   ├── zh-CN.js           # 中文语言包
│   ├── en-US.js           # 英文语言包
│   └── th-TH.js           # 泰文语言包
├── router/          # 路由配置
├── views/           # 页面组件
│   ├── Index.vue           # 主页面
│   ├── OrderScan/          # 扫描订单模块
│   │   └── Index.vue
│   └── InStorage/          # 入库管理模块
│       ├── Index.vue       # 入库列表页
│       └── Details.vue     # 入库详情页
└── main.js          # 应用入口
```

## 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 页面路由

- `/` - 重定向到主页
- `/index` - 主页面
- `/order-scan` - 扫描订单页面
- `/in-storage` - 入库管理页面
- `/in-storage/details` - 入库详情页面

## 浏览器兼容性

- Chrome (Android) 70+
- Safari (iOS) 12+
- Firefox Mobile 68+
- Edge Mobile 79+

## 部署说明

本系统专为PDA设备设计，建议部署到内网环境，确保设备能够正常访问后端API接口。

## 开发注意事项

1. 所有页面都已针对移动端优化
2. 使用了CSS Grid和Flexbox进行布局
3. 添加了触摸设备的特殊样式
4. 实现了页面间的平滑过渡动画
5. 支持PWA特性（可根据需要扩展）

## 🎯 使用说明

### 语言切换操作
1. 查看屏幕右下角的悬浮语言按钮（显示当前语言的国旗）
2. 点击按钮打开语言选择菜单
3. 选择需要的语言（中文/English/ไทย）
4. 页面内容立即切换到选择的语言
5. 语言设置会自动保存，下次访问时保持选择

### 入库管理操作
1. 在主页面点击"入库管理"进入订单查询页面
2. 输入订单号并点击确认按钮
3. 系统调用 `getOrderById` 方法获取订单详情
4. 订单详情以列表形式展示，包含完整的产品和订单信息
5. 点击任意订单详情项可跳转到详情页面进行具体操作

### API 数据格式
- 支持标准的订单详情数据格式（详见 `API_DATA_FORMAT.md`）
- 包含 15 个字段的完整订单信息
- **订单状态支持**: BillStatus 使用数值状态码 (0-5)
  - 0: 未审核 | 1: 已审核 | 2: 操作中
  - 3: 已作废 | 4: 自动结单 | 5: 手动结单
- 完整的状态国际化支持（中文/英文/泰文）
- 状态颜色区分显示，直观易识别

### 国际化开发指南
1. 所有文本都通过 `t()` 函数进行翻译
2. 语言文件位于 `src/locales/` 目录
3. 支持参数化翻译：`t('key', { param: value })`
4. 新增文本需要在三个语言文件中都添加对应翻译

## 后续扩展

- [x] 添加多语言支持（中文/英文/泰文）
- [ ] 添加扫码功能集成
- [ ] 实现离线数据缓存
- [ ] 添加推送通知
- [ ] 集成打印功能
- [ ] 添加更多语言支持



