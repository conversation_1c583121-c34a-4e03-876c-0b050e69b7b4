<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts';
import {request} from '@/utils/request.ts';


// 路由相关
const route = useRoute()
const router = useRouter()

// 仓库数据
const storageData = reactive({
  // 入库数和出库数
  keyData: {
    inCount: 0,
    outCount: 0,
    positionAllCount: 0,
    positionBusyCount: 0,
    positionEmptyCount: 0,
    positionFreeCount: 0
  },
  // 库存占用率数据
  storageUsage: {
    empty: 0,
    inProgress: 0,
    occupied: 0
  },
  // 库存物料状态数据
  storageStatus: [] as Array<{name: string, value: number}>,
  // 货架数据
  shelves: [
    { floor: 7, code: 'F7', selected: true },
    { floor: 6, code: 'F6', selected: true },
    { floor: 5, code: 'F5', selected: true },
    { floor: 4, code: 'F4', selected: true },
    { floor: 3, code: 'F3', selected: true },
    { floor: 2, code: 'F2', selected: true },
    { floor: 1, code: 'F1', selected: true }
  ],
  // 设备图片数据
  devices: [
    { id: 1, name: '堆垛机', image: '/static/imgs/stacker.png', status: [] as Array<{id: string, status: string, date: string}> },
    { id: 2, name: '输送线', image: '/static/imgs/conveyor.png', status: [] as Array<{id: string, status: string, date: string}> }
  ],
  // 七天数据统计
  palletStats: {
    dates: [] as string[],
    inbound: [] as number[],
    outbound: [] as number[],
    remove: [] as number[]
  },
  // 任务列表
  tasks: []
});

// 视图切换 - 前、后、左、右
const currentView = ref('前');
const changeView = (view: string) => {
  currentView.value = view;
};

// 返回功能
const goBack = () => {
  console.log('返回功能',window.history);
  // 优先使用浏览器历史记录回退
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    // 如果没有历史记录，跳转到Wcs_Simulator首页
    router.push({ name: 'storage_home' });
  }
};

// 清除定时器
let refreshInterval: number;

// 页面渲染key，用于强制重新渲染
const pageKey = ref(0);

// 监听路由变化，确保页面能正确响应浏览器回退

watch(() => route.name, (newRouteName) => {
  if (newRouteName === 'storage_Information') {
    console.log('路由切换到storage_Information页面');
    pageKey.value++;

    nextTick(() => {
      initStorageUsageChart();
      initStorageStatusChart();
      initPalletStatsChart();
    });
  }
}, { immediate: true });

// 添加浏览器回退处理
const handlePopState = () => {
  console.log('浏览器回退事件触发');
  // 强制重新渲染
  pageKey.value++;
};

onMounted(() => {
  // 监听浏览器回退事件
  window.addEventListener('popstate', handlePopState);

  // 原有的初始化代码...
  fetchKeyDpValue();
  fetchTaskList();
  fetchPositionCountByRow();
  fetchTaskHistorySevenDaySummary();
  fetchDeviceStatus();
  initStorageUsageChart();
  initStorageStatusChart();
  initPalletStatsChart();

  // 定时刷新数据 - 每10秒刷新一次
  refreshInterval = setInterval(() => {
    fetchKeyDpValue();
    fetchTaskList();
    fetchPositionCountByRow();
    fetchTaskHistorySevenDaySummary();
    fetchDeviceStatus();
  }, 10000);
});

onUnmounted(() => {
  // 移除浏览器回退事件监听
  window.removeEventListener('popstate', handlePopState);
  clearInterval(refreshInterval);
});

// 获取关键数据
const fetchKeyDpValue = async () => {
  try {
    // const response = await request.get('/api/Wms_Position/GetKeyDpValue', {}, false);
    const response = await request.get('/api/Wms_Position/GetKeyDpValue', {});
    if (response && response.Data) {
      // 处理返回的数据
      const data = response.Data;

      // 查找并设置入库和出库数据
      data.forEach((item:any) => {
        switch (item.Key) {
          case 'TaskInCount':
            storageData.keyData.inCount = parseInt(item.Value) || 0;
            break;
          case 'TaskOutCount':
            storageData.keyData.outCount = parseInt(item.Value) || 0;
            break;
          case 'PositionAllCount':
            storageData.keyData.positionAllCount = parseInt(item.Value) || 0;
            break;
          case 'PostitBusyCount':
            storageData.keyData.positionBusyCount = parseInt(item.Value) || 0;
            break;
          case 'PostitEmptyCount':
            storageData.keyData.positionEmptyCount = parseInt(item.Value) || 0;
            break;
          case 'PostitFreeCount':
            storageData.keyData.positionFreeCount = parseInt(item.Value) || 0;
            break;
        }
      });
      console.log(storageData.keyData);
      // 更新库存占用率数据
      const totalPositions = storageData.keyData.positionAllCount || 1; // 防止除以0
      storageData.storageUsage.occupied = (storageData.keyData.positionBusyCount / totalPositions) * 100;
      storageData.storageUsage.inProgress = (storageData.keyData.positionEmptyCount / totalPositions) * 100;
      storageData.storageUsage.empty = (storageData.keyData.positionFreeCount / totalPositions) * 100;
      console.log(storageData.storageUsage);
      // 更新图表
      initStorageUsageChart();
    }
  } catch (error) {
    console.error('获取关键数据失败:', error);
  }
};

// 获取任务列表数据
const fetchTaskList = async () => {
  try {
    const response = await request.get('/api/Wms_Position/GetTaskList', {});
    if (response && response.Data) {
      storageData.tasks = response.Data;
      console.log(storageData.tasks);
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  }
};

// 获取库存物料情况数据
const fetchPositionCountByRow = async () => {
  try {
    const response = await request.get('/api/Wms_Position/PositionCountByRow', {});
    if (response && response.Data) {
      const data = response.Data;
      // 转换数据格式为图表所需的格式
      const chartData = [];

      // 遍历返回的数据，转换为图表需要的格式
      for (const key in data) {
        if (key !== 'All') {  // 排除总数
          chartData.push({
            name: `第${key}层`,
            value: data[key]
          });
        }
      }
      chartData.push({
        name: '总计',
        value: data['All']
      });


      // 更新库存物料状态数据
      storageData.storageStatus = chartData;

      // 更新图表
      initStorageStatusChart();
    }
  } catch (error) {
    console.error('获取库存物料情况失败:', error);
  }
};

// 获取七天数据统计
const fetchTaskHistorySevenDaySummary = async () => {
  try {
    // const response = await request.get('/api/Wms_Position/TaskHistorySevenDaySummary', {}, false);
    const response = await request.get('/api/Wms_Position/TaskHistorySevenDaySummary', {});
    if (response && response.Data && Array.isArray(response.Data)) {
      const data = response.Data;

      // 获取表头信息（第一个数组）
      const headers = data[0];

      // 找到"整盘入库"和"空盘出库"的索引
      const inboundIndex = headers.indexOf("整盘入库");
      const outboundIndex = headers.indexOf("整盘出库");
      const removeIndex = headers.indexOf("移库作业");

      // 准备日期和数据数组
      const dates: string[] = [];
      const inbound: number[] = [];
      const outbound: number[] = [];
      const remove: number[] = [];

      // 从后往前获取最近7天的数据
      // 从数组末尾开始，向前获取7条数据
      const dataLength = data.length;
      let count = 0;

      // 从最后一个元素开始，向前遍历
      for (let i = dataLength - 1; i >= 1 && count < 7; i--) {
        const row = data[i];
        if (row && row.length > 0) {
          // 第一列是日期
          dates.unshift(row[0]);

          // 获取整盘入库和空盘出库数据
          inbound.unshift(parseInt(row[inboundIndex]) || 0);
          outbound.unshift(parseInt(row[outboundIndex]) || 0);
          remove.unshift(parseInt(row[removeIndex]) || 0);

          count++;
        }
      }

      // 更新数据
      storageData.palletStats.dates = dates;
      storageData.palletStats.inbound = inbound;
      storageData.palletStats.outbound = outbound;
      storageData.palletStats.remove = remove;

      // 更新图表
      initPalletStatsChart();
    }
  } catch (error) {
    console.error('获取七天数据统计失败:', error);
  }
};

// 获取设备状态数据
const fetchDeviceStatus = async () => {
  try {
    const response = await request.post('/api/Wcs_PlcDataItem/ReadAllDeviceStatus', {});
    if (response && response.status && response.data) {
      // 清空当前设备状态数据
      storageData.devices.forEach(device => {
        device.status = [];
      });

      // 处理返回的设备状态数据
      response.data.forEach((item: { name: string, status: string, repairDate: string }) => {
        // 根据设备名称判断类型，分配到对应的设备中
        if (item.name.includes('堆垛机')) {
          // 堆垛机添加到id为1的设备
          const stackerDevice = storageData.devices.find(d => d.id === 1);
          if (stackerDevice) {
            stackerDevice.status.push({
              id: item.name, // 使用name作为id
              status: item.status,
              date: item.repairDate
            });
          }
        } else if (item.name.includes('工位') || item.name.includes('站台')) {
          // 工位/站台添加到id为2的设备
          const conveyorDevice = storageData.devices.find(d => d.id === 2);
          if (conveyorDevice) {
            conveyorDevice.status.push({
              id: item.name, // 使用name作为id
              status: item.status,
              date: item.repairDate
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('获取设备状态数据失败:', error);
  }
};

// 库存占用率饼图
const initStorageUsageChart = () => {
  const chartDom = document.getElementById('storage-usage-chart');
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['空盘率', '空货率', '物料率'],
      textStyle: {
        color: '#fff'
      }
    },
    color: ['#e74c3c', '#3498db', '#2ecc71'],
    series: [
      {
        name: '库存占用率',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: storageData.storageUsage.empty, name: '空盘率' },
          { value: storageData.storageUsage.inProgress, name: '空货率' },
          { value: storageData.storageUsage.occupied, name: '物料率' }
        ]
      }
    ]
  };
  myChart.setOption(option);

  // 窗口大小变化时重绘图表
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 库存物料情况柱状图
const initStorageStatusChart = () => {
  const chartDom = document.getElementById('storage-status-chart');
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: storageData.storageStatus.map(item => item.name),
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          color: '#fff'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#fff'
        }
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        data: storageData.storageStatus.map(item => item.value),
        itemStyle: {
          color: '#e74c3c'
        }
      }
    ]
  };
  myChart.setOption(option);

  // 窗口大小变化时重绘图表
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 七天数据统计图表
const initPalletStatsChart = () => {
  const chartDom = document.getElementById('pallet-stats-chart');
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['入库', '出库','移库'],
      textStyle: {
        color: '#fff',
        fontSize: 10 // 减小图例字体大小
      },
      itemWidth: 10, // 减小图例标记大小
      itemHeight: 10,
      right: 10, // 将图例放在右侧
      top: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%', // 为图例留出空间
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: storageData.palletStats.dates,
        axisLabel: {
          color: '#fff',
          fontSize: 9, // 减小x轴标签字体大小
          rotate: 30 // 旋转标签以节省空间
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 9 // 减小y轴标签字体大小
        }
      }
    ],
    series: [
      {
        name: '入库',
        type: 'line',
        // stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: storageData.palletStats.inbound,
        itemStyle: {
          color: '#e74c3c'
        },
        symbolSize: 4 // 减小数据点大小
      },
      {
        name: '出库',
        type: 'line',
        // stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: storageData.palletStats.outbound,
        itemStyle: {
          color: '#3498db'
        },
        symbolSize: 4 // 减小数据点大小
      } ,{
        name: '移库',
        type: 'line',
        // stack: '总量',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: storageData.palletStats.remove,
        itemStyle: {
          color: '#dbbf34'
        },
        symbolSize: 4 // 减小数据点大小
      }
    ]
  };
  myChart.setOption(option);

  // 窗口大小变化时重绘图表
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

</script>

<template>
  <div class="storage-dashboard" :key="pageKey">
    <!-- 顶部标题区域 -->
    <div class="dashboard-header">

      <div class="title">智能仓储系统实时监控</div>

      <!-- 返回按钮 -->
      <button class="back-button" @click="goBack" title="返回">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>

      <!-- 主体内容区域 -->
      <div class="dashboard-content">

        <!-- 左侧区域 -->
        <div class="left-panel">
          <!--        <Decoration1 style="width:200px; height:50px;" />-->
          <!-- 入库和出库信息 -->
          <div class="info-circles">
            <div class="circle operation">
              <div class="circle-content">
                <div class="circle-title">今日入库数</div>
                <div class="circle-count">{{ storageData.keyData.inCount }}条</div>
              </div>
            </div>
            <div class="circle error">
              <div class="circle-content">
                <div class="circle-title">今日出库数</div>
                <div class="circle-count">{{ storageData.keyData.outCount }}条</div>
              </div>
            </div>
          </div>

          <!-- 库存占用率 -->
          <div class="chart-panel">
            <div class="panel-title">
              <span class="icon">●</span> 库存占用率
            </div>
            <div id="storage-usage-chart" class="chart-container"></div>
          </div>

          <!-- 库存物料状态 -->
          <div class="chart-panel">
            <div class="panel-title">
              <span class="icon">●</span> 库存物料情况
            </div>
            <div id="storage-status-chart" class="chart-container"></div>
          </div>
        </div>

        <!-- 中间区域 - 货架可视化 -->
        <div class="center-panel">
          <div class="shelf-visualization">
            <!-- 3D 货架展示 -->
            <div class="shelf-3d-view">
              <!-- 这里可以放置 3D 渲染组件 -->
              <p>货架区域</p>
            </div>
            <!-- 视图切换按钮 -->
            <div class="view-controls">
              <button @click="changeView('前')" :class="{ active: currentView === '前' }">前</button>
              <button @click="changeView('后')" :class="{ active: currentView === '后' }">后</button>
              <button @click="changeView('左')" :class="{ active: currentView === '左' }">左</button>
              <button @click="changeView('右')" :class="{ active: currentView === '右' }">右</button>
            </div>
          </div>

          <div class="task-panel">
            <h2 class="panel-title">任务列表</h2>

            <div class="task-table" style="width: 100%;">
              <table style="width: 100%;">
                <thead>
                <tr>
                  <th>任务编号</th>
                  <th>设备任务号</th>
                  <th>托盘号</th>
                  <th>任务类型</th>
                  <th>运行进程号</th>
                  <th>物料编号</th>
                  <th>物料名称</th>
                  <th>批次</th>
                  <th>数量</th>
                  <th>起始仓位</th>
                  <th>目的仓位</th>
                  <th>起始站台</th>
                  <th>目的站台</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="task in storageData.tasks" :key="task.TaskId">
                  <td>{{ task.TaskNo }}</td>
                  <td>{{ task.WorkNo }}</td>
                  <td>{{ task.PalletNo }}</td>
                  <td>{{ task.OperateName }}</td>
                  <td>{{ task.Design }}</td>
                  <td>{{ task.MaterialNo }}</td>
                  <td>{{ task.MaterialName }}</td>
                  <td>{{ task.Batch }}</td>
                  <td>{{ task.Quantity }}</td>
                  <td>{{ task.SrcPositionNo || '-' }}</td>
                  <td>{{ task.DestPositionNo || '-' }}</td>
                  <td>{{ task.SrcStation || '-' }}</td>
                  <td>{{ task.DestStation || '-' }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <Decoration2 :reverse="true" style="width:5px; position: absolute;height:30%;right:calc(22% + 6px);bottom:0;" />
        <!-- 右侧区域 -->
        <div class="right-panel">
          <!-- 设备图片和状态 -->
          <div class="devices-panel">
            <div class="panel-title">设备图片</div>
            <div class="devices-container">
              <div v-for="device in storageData.devices" :key="device.id" class="device-item">
                <div class="device-image">
                  <img :src="device.image" :alt="device.name" />
                </div>
                <div class="device-status">
                  <table>
                    <thead>
                    <tr>
                      <th>设备名称</th>
                      <th>状态</th>
                      <th>下次保养时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="status in device.status" :key="status.id">
                      <td :class="{
                      'status-running': status.status === '运行中',
                      'status-stopped': status.status === '停止' || status.status === '未运行' || status.status === '空闲',
                      'status-maintenance': status.status === '检修' || status.status === '故障'
                    }">{{ status.id }}</td>
                      <td :class="{
                      'status-running': status.status === '运行中' ,
                      'status-stopped': status.status === '停止' || status.status === '未运行'|| status.status === '空闲',
                      'status-maintenance': status.status === '检修' || status.status === '故障'
                    }">{{ status.status }}</td>
                      <td>{{ status.date }}</td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <Decoration2 style="width:100%; height:5px;" />
          <!-- 板数统计 -->
          <div class="chart-panel">
            <div class="panel-title">
              <span class="icon">●</span> 七天数据统计
            </div>
            <div id="pallet-stats-chart" class="chart-container"></div>
          </div>
        </div>
      </div>

  </div>


</template>

<style scoped lang="less">
.dv-border-box-content {

  height: 100% !important;
  display: flex;          /* 视具体需求加flex布局 */
  flex-direction: column; /* 让子元素纵向排列 */
}
.storage-dashboard {
  width: 100%;

  height: 100vh;
  background-color: #000c1e; /* 深色背景 */
  color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止主容器滚动 */
  padding: 10px;
  box-sizing: border-box;

  .dashboard-header {
    height: 60px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    text-align: center;
    position: relative;
    overflow: hidden;
    line-height: 60px;
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 10px #00aaff;
    animation: glow 2s ease-in-out infinite alternate;
  }

  .back-button {
    position: absolute;
    top: 15px;
    left: 20px;
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid #3498db;
    color: #3498db;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    z-index: 1000;
    line-height: 1;
  }

  .back-button:hover {
    background: rgba(52, 152, 219, 0.3);
    border-color: #2980b9;
    color: #2980b9;
    transform: translateX(-2px);
  }

  .back-button svg {
    width: 16px;
    height: 16px;
  }

  .dashboard-content {
    flex: 1;

    height: 100%; /* header 高度 */
    display: flex;
    gap: 8px; /* 减小间距 */
    overflow: hidden; /* 防止内容区滚动 */
    padding-top: 8px; /* 减小与header间距 */
  }

  .left-panel, .right-panel, .center-panel {
    animation: slideUpFadeIn 0.8s ease-out forwards;
    opacity: 0;
    overflow: auto; /* 允许面板内滚动 */
  }

  .left-panel {
    animation-delay: 0.2s;
    width: 22%; /* 减小左侧面板宽度 */
  }

  .center-panel {
    animation-delay: 0.4s;
    flex: 1;
  }

  .right-panel {
    animation-delay: 0.6s;
    width: 22%; /* 减小右侧面板宽度 */
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 100%; /* 确保不超出容器 */
  }

  .left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .chart-panel, .info-panel, .devices-panel, .shelf-visualization, .task-panel {
    background-color: rgba(10, 17, 33, 0.8); /* 半透明背景 */
    border: 1px solid #1a2a4a;
    border-radius: 8px;
    padding: 10px; /* 减小内边距 */
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 15px rgba(0, 170, 255, 0.2);
  }

  .chart-panel {
    flex: 1;
    min-height: 0; /* Ensure chart can shrink if needed */
    height: 33%; /* 设置图表面板高度 */
  }

  .panel-title {
    margin: 0 0 10px 0; /* 减小标题下边距 */
    font-size: 16px; /* 减小标题字体大小 */
    color: #00aaff;
    border-bottom: 1px solid #1a2a4a;
    padding-bottom: 8px;
  }

  .shelf-visualization {
    flex: 1;
    position: relative;
    justify-content: center;
    align-items: center;
    min-height: 0; /* flexbox fix */
    max-height: 60%; /* 限制可视化区域高度 */
  }

  .task-panel {
    flex: 1; /* 任务列表高度 */
    min-height: 0; /* flexbox fix */
    max-height: 40%; /* 限制任务列表高度 */

    .task-table {
      overflow-y: auto;
      flex: 1; /* 占据剩余空间 */

      table {
        width: 100%;
        border-collapse: collapse;

        th, td {
          padding: 3px; /* 减小单元格内边距 */
          text-align: center;
          border: 1px solid #1a2a4a;
          font-size: 11px; /* 减小字体大小 */
        }

        th {
          background-color: #1e2b4a;
          color: #3498db;
          font-size: 11px; /* 减小字体大小 */
          white-space: nowrap;
        }

        td {
          font-size: 10px; /* 更小的字体 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100px; /* 限制单元格宽度 */
        }

        /* 任务编号列宽度设置 */
        th:first-child, td:first-child {
          min-width: 120px;
          max-width: 150px;
        }
      }
    }
  }

  .shelf-3d-view {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px; /* 减小字体大小 */
    color: #555;
    background: #050d1a;
    border-radius: 4px;
  }

  .view-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 4px; /* 减小按钮间距 */

    button {
      padding: 3px 6px; /* 减小按钮内边距 */
      font-size: 11px; /* 减小按钮字体大小 */
    }
  }

  .info-circles {
    display: flex;
    gap: 15px; /* 减小圆圈间距 */
    justify-content: space-around;
    padding: 8px 0; /* 减小上下内边距 */

    .circle {
      width: 100px; /* 减小圆圈大小 */
      height: 100px; /* 减小圆圈大小 */
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border: 2px solid; /* 减小边框厚度 */
      animation: pulse 2.5s infinite;

      &.operation {
        border-color: #3498db;
        box-shadow: 0 0 10px #3498db;
        animation-delay: 0s;
      }

      &.error {
        border-color: #e74c3c;
        box-shadow: 0 0 10px #e74c3c;
        animation-delay: 0.5s;
      }

      .circle-content {
        text-align: center;

        .circle-title {
          font-size: 14px; /* 减小标题字体大小 */
          margin-bottom: 6px; /* 减小下边距 */
        }

        .circle-count {
          font-size: 20px; /* 减小计数字体大小 */
          font-weight: bold;
        }
      }
    }
  }

  .chart-container {
    flex: 1;
    min-height: 150px; /* 设置最小高度 */
  }

  .devices-panel {
    background-color: #0a1121;
    border: 1px solid #1a2a4a;
    border-radius: 8px;
    padding: 10px; /* 减小内边距 */
    flex: 1; /* 占据剩余空间 */
    min-height: 0; /* 防止flex溢出 */
    max-height: 55%; /* 限制设备面板高度 */
    overflow: auto; /* 允许内容滚动 */

    .devices-container {
      display: flex;
      flex-direction: column;
      gap: 15px; /* 减小设备项间距 */
      overflow-y: auto; /* 启用滚动 */
      flex: 1; /* 填充可用空间 */
      min-height: 0; /* 在flexbox中滚动所需 */

      .device-item {
        display: flex;
        gap: 10px; /* 减小间距 */
        align-items: center;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: rgba(0, 170, 255, 0.1);
        }

        .device-image {
          flex: 0 0 80px; /* 减小图片区域宽度 */
          text-align: center;

          img {
            max-width: 100%;
            max-height: 70px; /* 减小图片最大高度 */
          }
        }

                .device-status {
          flex: 1;
          table {
            width: 100%;
            border-collapse: collapse;

            th, td {
              padding: 4px; /* 减小单元格内边距 */
              text-align: center;
              border: 1px solid #1a2a4a;
              font-size: 10px; /* 减小字体大小 */
            }

            th {
              background-color: #1e2b4a;
              color: #3498db;
            }

            tbody tr {
              transition: background-color 0.3s ease;

              &:hover {
                background-color: rgba(0, 170, 255, 0.15);
              }
            }

            .status-running {
              color: #2ecc71;
            }

            .status-stopped {
              color: #95a5a6;
            }

            .status-maintenance {
              color: #e74c3c;
            }
          }
        }
      }
    }
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px; /* 减小滚动条宽度 */
    height: 6px; /* 减小滚动条高度 */
  }
  ::-webkit-scrollbar-track {
    background: #0a1121;
  }
  ::-webkit-scrollbar-thumb {
    background: #1a2a4a;
    border-radius: 3px; /* 减小圆角 */
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #00aaff;
  }

  /* Animations */
  @keyframes slideUpFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes glow {
    from {
      text-shadow: 0 0 10px #00aaff, 0 0 20px #00aaff;
    }
    to {
      text-shadow: 0 0 20px #00aaff, 0 0 30px #00aaff;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 15px var(--pulse-color, #3498db);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 25px var(--pulse-color, #3498db);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 15px var(--pulse-color, #3498db);
    }
  }

  .info-circles .circle.operation {
    --pulse-color: #3498db;
  }
  .info-circles .circle.error {
    --pulse-color: #e74c3c;
  }
}
</style>